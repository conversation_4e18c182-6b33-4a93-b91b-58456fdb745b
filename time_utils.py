#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Time Utilities
时间处理工具函数模块，提供时间转换、时间范围计算等功能
"""

import time
from datetime import datetime, timedelta
from typing import Tuple, Optional


def convert_ms_to_datetime(ms_timestamp: int) -> str:
    """
    将毫秒时间戳转换为ISO 8601日期时间字符串
    
    Args:
        ms_timestamp: 毫秒时间戳
        
    Returns:
        ISO 8601格式的日期时间字符串
    """
    try:
        return datetime.fromtimestamp(ms_timestamp / 1000).isoformat()
    except (ValueError, OSError, OverflowError):
        return ""


def convert_datetime_to_ms(datetime_str: str) -> int:
    """
    将ISO 8601日期时间字符串转换为毫秒时间戳
    
    Args:
        datetime_str: ISO 8601格式的日期时间字符串
        
    Returns:
        毫秒时间戳
    """
    try:
        dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        return int(dt.timestamp() * 1000)
    except (ValueError, TypeError):
        return 0


def get_current_time_ms() -> int:
    """
    获取当前时间的毫秒时间戳
    
    Returns:
        当前时间的毫秒时间戳
    """
    return int(time.time() * 1000)


def calculate_time_range_hours(start_date: str = "2024-01-01", end_date: str = "2025-08-31") -> int:
    """
    计算指定日期范围的小时数
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD格式)
        end_date: 结束日期 (YYYY-MM-DD格式)
        
    Returns:
        时间范围的小时数
    """
    try:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
        hours = int((end - start).total_seconds() / 3600)
        return hours
    except ValueError:
        # 默认返回从2024年1月1日到2025年8月31日的小时数
        start = datetime(2024, 1, 1)
        end = datetime(2025, 8, 31, 23, 59, 59)
        return int((end - start).total_seconds() / 3600)


def get_time_range_ms(hours: int) -> Tuple[int, int]:
    """
    获取指定小时数的时间范围（毫秒时间戳）
    
    Args:
        hours: 时间范围小时数
        
    Returns:
        (开始时间戳, 结束时间戳) 的元组
    """
    current_time_ms = get_current_time_ms()
    start_time_ms = current_time_ms - (hours * 60 * 60 * 1000)
    return start_time_ms, current_time_ms


def get_fixed_date_range_ms() -> Tuple[int, int]:
    """
    获取固定日期范围的时间戳（2024-01-01 到 2025-08-31）
    
    Returns:
        (开始时间戳, 结束时间戳) 的元组
    """
    start_time_ms = int(datetime(2024, 1, 1).timestamp() * 1000)
    end_time_ms = int(datetime(2025, 8, 31, 23, 59, 59).timestamp() * 1000)
    return start_time_ms, end_time_ms


def format_duration(seconds: float) -> str:
    """
    格式化持续时间为可读字符串
    
    Args:
        seconds: 持续时间（秒）
        
    Returns:
        格式化的持续时间字符串
    """
    if seconds < 60:
        return f"{seconds:.2f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f} minutes"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.2f} hours"
    else:
        days = seconds / 86400
        return f"{days:.2f} days"


def is_time_near_boundary(timestamp_ms: int, boundary_ms: int, threshold_hours: int = 1) -> bool:
    """
    检查时间戳是否接近边界时间
    
    Args:
        timestamp_ms: 要检查的时间戳（毫秒）
        boundary_ms: 边界时间戳（毫秒）
        threshold_hours: 阈值小时数
        
    Returns:
        是否接近边界
    """
    threshold_ms = threshold_hours * 60 * 60 * 1000
    return abs(timestamp_ms - boundary_ms) < threshold_ms


def get_time_window_info(start_time_ms: int, end_time_ms: int) -> dict:
    """
    获取时间窗口信息
    
    Args:
        start_time_ms: 开始时间戳（毫秒）
        end_time_ms: 结束时间戳（毫秒）
        
    Returns:
        包含时间窗口信息的字典
    """
    duration_ms = end_time_ms - start_time_ms
    duration_hours = duration_ms / (1000 * 60 * 60)
    
    return {
        'start_time_ms': start_time_ms,
        'end_time_ms': end_time_ms,
        'start_datetime': convert_ms_to_datetime(start_time_ms),
        'end_datetime': convert_ms_to_datetime(end_time_ms),
        'duration_ms': duration_ms,
        'duration_hours': duration_hours,
        'duration_formatted': format_duration(duration_ms / 1000)
    }


def validate_time_range(start_time_ms: int, end_time_ms: int) -> bool:
    """
    验证时间范围是否有效
    
    Args:
        start_time_ms: 开始时间戳（毫秒）
        end_time_ms: 结束时间戳（毫秒）
        
    Returns:
        时间范围是否有效
    """
    return start_time_ms > 0 and end_time_ms > 0 and start_time_ms < end_time_ms


def get_relative_time_description(timestamp_ms: int) -> str:
    """
    获取相对时间描述
    
    Args:
        timestamp_ms: 时间戳（毫秒）
        
    Returns:
        相对时间描述字符串
    """
    current_ms = get_current_time_ms()
    diff_seconds = (current_ms - timestamp_ms) / 1000
    
    if diff_seconds < 0:
        return "in the future"
    elif diff_seconds < 60:
        return f"{int(diff_seconds)} seconds ago"
    elif diff_seconds < 3600:
        minutes = int(diff_seconds / 60)
        return f"{minutes} minutes ago"
    elif diff_seconds < 86400:
        hours = int(diff_seconds / 3600)
        return f"{hours} hours ago"
    else:
        days = int(diff_seconds / 86400)
        return f"{days} days ago"


def create_time_buckets(start_time_ms: int, end_time_ms: int, bucket_hours: int = 1) -> list:
    """
    创建时间桶列表
    
    Args:
        start_time_ms: 开始时间戳（毫秒）
        end_time_ms: 结束时间戳（毫秒）
        bucket_hours: 每个桶的小时数
        
    Returns:
        时间桶列表，每个桶包含开始和结束时间戳
    """
    buckets = []
    bucket_ms = bucket_hours * 60 * 60 * 1000
    current_start = start_time_ms
    
    while current_start < end_time_ms:
        current_end = min(current_start + bucket_ms, end_time_ms)
        buckets.append({
            'start_ms': current_start,
            'end_ms': current_end,
            'start_datetime': convert_ms_to_datetime(current_start),
            'end_datetime': convert_ms_to_datetime(current_end)
        })
        current_start = current_end
    
    return buckets


def parse_time_string(time_str: str) -> Optional[int]:
    """
    解析时间字符串为毫秒时间戳
    支持多种格式：ISO 8601, Unix timestamp, 相对时间等
    
    Args:
        time_str: 时间字符串
        
    Returns:
        毫秒时间戳，解析失败返回None
    """
    if not time_str:
        return None
    
    # 尝试解析Unix时间戳
    try:
        if time_str.isdigit():
            timestamp = int(time_str)
            # 判断是秒还是毫秒
            if timestamp > 1000000000000:  # 毫秒时间戳
                return timestamp
            else:  # 秒时间戳
                return timestamp * 1000
    except ValueError:
        pass
    
    # 尝试解析ISO 8601格式
    try:
        return convert_datetime_to_ms(time_str)
    except:
        pass
    
    return None
