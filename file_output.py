#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Output and Logging Management
文件输出和日志管理模块，负责将所有输出同时保存到文件和控制台
"""

import os
from datetime import datetime
from typing import Optional


class FileOutputManager:
    """文件输出管理器，负责将所有输出同时保存到文件"""
    
    def __init__(self, output_file: str = "ocpp_exporter_output.txt"):
        """
        初始化文件输出管理器
        
        Args:
            output_file: 输出文件路径
        """
        self.output_file = output_file
        self.ensure_output_directory()
    
    def ensure_output_directory(self):
        """确保输出目录存在"""
        directory = os.path.dirname(self.output_file)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
    
    def save_output(self, content: str, add_timestamp: bool = True):
        """
        保存内容到文件
        
        Args:
            content: 要保存的内容
            add_timestamp: 是否添加时间戳
        """
        try:
            timestamp = f"[{datetime.now().isoformat()}] " if add_timestamp else ""
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp}{content}\n")
                f.flush()
        except Exception as e:
            print(f"Failed to save output to file: {e}")
    
    def save_and_print(self, content: str, add_timestamp: bool = True):
        """
        同时打印到控制台和保存到文件
        
        Args:
            content: 要输出的内容
            add_timestamp: 是否添加时间戳
        """
        print(content)
        self.save_output(content, add_timestamp)
    
    def save_separator(self, char: str = "=", length: int = 100):
        """
        保存分隔符
        
        Args:
            char: 分隔符字符
            length: 分隔符长度
        """
        separator = char * length
        self.save_and_print(separator)
    
    def save_header(self, title: str, char: str = "=", length: int = 100):
        """
        保存标题头
        
        Args:
            title: 标题内容
            char: 分隔符字符
            length: 分隔符长度
        """
        self.save_separator(char, length)
        self.save_and_print(title)
        self.save_separator(char, length)
    
    def save_section(self, title: str, content: str, char: str = "-", length: int = 80):
        """
        保存带标题的内容段落
        
        Args:
            title: 段落标题
            content: 段落内容
            char: 分隔符字符
            length: 分隔符长度
        """
        self.save_separator(char, length)
        self.save_and_print(title)
        self.save_separator(char, length)
        self.save_and_print(content)
    
    def save_list(self, title: str, items: list, numbered: bool = False):
        """
        保存列表内容
        
        Args:
            title: 列表标题
            items: 列表项
            numbered: 是否使用数字编号
        """
        self.save_and_print(f"\n{title}:")
        for i, item in enumerate(items, 1):
            if numbered:
                self.save_and_print(f"  {i}. {item}")
            else:
                self.save_and_print(f"  • {item}")
    
    def save_dict(self, title: str, data: dict, indent: int = 2):
        """
        保存字典内容
        
        Args:
            title: 字典标题
            data: 字典数据
            indent: 缩进空格数
        """
        self.save_and_print(f"\n{title}:")
        indent_str = " " * indent
        for key, value in data.items():
            self.save_and_print(f"{indent_str}{key}: {value}")
    
    def save_json(self, title: str, data: dict):
        """
        保存JSON格式内容
        
        Args:
            title: JSON标题
            data: 要保存的数据
        """
        import json
        self.save_and_print(f"\n{title}:")
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        for line in json_str.split('\n'):
            self.save_and_print(f"  {line}")
    
    def save_table(self, title: str, headers: list, rows: list):
        """
        保存表格内容
        
        Args:
            title: 表格标题
            headers: 表头列表
            rows: 行数据列表
        """
        self.save_and_print(f"\n{title}:")
        
        # 计算列宽
        col_widths = [len(str(header)) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # 打印表头
        header_line = " | ".join(str(headers[i]).ljust(col_widths[i]) for i in range(len(headers)))
        self.save_and_print(f"  {header_line}")
        
        # 打印分隔线
        separator_line = " | ".join("-" * col_widths[i] for i in range(len(headers)))
        self.save_and_print(f"  {separator_line}")
        
        # 打印数据行
        for row in rows:
            row_line = " | ".join(str(row[i] if i < len(row) else "").ljust(col_widths[i]) for i in range(len(headers)))
            self.save_and_print(f"  {row_line}")
    
    def get_output_file(self) -> str:
        """获取输出文件路径"""
        return self.output_file
    
    def clear_file(self):
        """清空输出文件"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write("")
        except Exception as e:
            print(f"Failed to clear output file: {e}")


def create_timestamped_filename(prefix: str = "ocpp_exporter_output", extension: str = "txt") -> str:
    """
    创建带时间戳的文件名
    
    Args:
        prefix: 文件名前缀
        extension: 文件扩展名
        
    Returns:
        带时间戳的文件名
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{prefix}_{timestamp}.{extension}"
