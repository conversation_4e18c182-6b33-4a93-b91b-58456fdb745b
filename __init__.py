#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP 1.6 Multi-Source Exporter Package
Python包初始化文件，定义包的公共接口和版本信息
"""

__version__ = "1.0.0"
__author__ = "OCPP Exporter Team"
__description__ = "OCPP 1.6 Compliant Charging Station Exporter with Multi-Source Support"

# 导入主要组件
from .constants import (
    OCPP_ERROR_CODES,
    CHARGE_POINT_STATUS,
    OCPP_MESSAGE_TYPES,
    DEFAULT_ES_SOURCES,
    DEFAULT_TARGET_INDEX,
    PROMETHEUS_DEFAULT_PORT,
    SCHEDULER_INTERVALS
)

from .file_output import (
    FileOutputManager,
    create_timestamped_filename
)

from .time_utils import (
    convert_ms_to_datetime,
    convert_datetime_to_ms,
    get_current_time_ms,
    calculate_time_range_hours,
    get_time_range_ms,
    get_fixed_date_range_ms,
    format_duration,
    is_time_near_boundary,
    get_time_window_info
)

from .document_parser import (
    OCPPDocumentParser,
    create_parser
)

from .elasticsearch_client import (
    ElasticsearchClient,
    create_client
)

from .transaction_analyzer import (
    TransactionAnalyzer,
    create_analyzer
)

from .message_type_detector import (
    MessageTypeDetector,
    create_detector
)

from .metrics_manager import (
    MetricsManager,
    create_metrics_manager
)

from .exporter import (
    OCPP16ChargingStationExporter,
    create_exporter
)

from .main import (
    main,
    main_with_custom_config,
    run_single_collection,
    run_analysis_only
)

# 定义包的公共接口
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    '__description__',
    
    # 常量
    'OCPP_ERROR_CODES',
    'CHARGE_POINT_STATUS',
    'OCPP_MESSAGE_TYPES',
    'DEFAULT_ES_SOURCES',
    'DEFAULT_TARGET_INDEX',
    'PROMETHEUS_DEFAULT_PORT',
    'SCHEDULER_INTERVALS',
    
    # 文件输出
    'FileOutputManager',
    'create_timestamped_filename',
    
    # 时间工具
    'convert_ms_to_datetime',
    'convert_datetime_to_ms',
    'get_current_time_ms',
    'calculate_time_range_hours',
    'get_time_range_ms',
    'get_fixed_date_range_ms',
    'format_duration',
    'is_time_near_boundary',
    'get_time_window_info',
    
    # 文档解析
    'OCPPDocumentParser',
    'create_parser',
    
    # Elasticsearch客户端
    'ElasticsearchClient',
    'create_client',
    
    # 事务分析
    'TransactionAnalyzer',
    'create_analyzer',
    
    # 消息类型检测
    'MessageTypeDetector',
    'create_detector',
    
    # 指标管理
    'MetricsManager',
    'create_metrics_manager',
    
    # 主导出器
    'OCPP16ChargingStationExporter',
    'create_exporter',
    
    # 主函数
    'main',
    'main_with_custom_config',
    'run_single_collection',
    'run_analysis_only'
]

# 包级别配置
import logging

# 设置默认日志级别
logging.getLogger(__name__).setLevel(logging.INFO)

# 包信息
def get_package_info():
    """获取包信息"""
    return {
        'name': 'ocpp_exporter',
        'version': __version__,
        'author': __author__,
        'description': __description__,
        'components': [
            'constants - OCPP 1.6标准常量定义',
            'file_output - 文件输出和日志管理',
            'time_utils - 时间处理工具函数',
            'document_parser - OCPP文档解析和验证',
            'elasticsearch_client - 多源Elasticsearch搜索',
            'transaction_analyzer - 事务配对分析引擎',
            'message_type_detector - 消息类型自动检测',
            'metrics_manager - Prometheus指标管理',
            'exporter - 主导出器类（集成所有模块）',
            'main - 程序入口点'
        ]
    }

def print_package_info():
    """打印包信息"""
    info = get_package_info()
    print(f"\n{info['name']} v{info['version']}")
    print(f"Author: {info['author']}")
    print(f"Description: {info['description']}")
    print("\nComponents:")
    for component in info['components']:
        print(f"  • {component}")
    print()

# 快速启动函数
def quick_start(es_sources=None, debug_mode=True, prometheus_port=8000):
    """
    快速启动OCPP导出器
    
    Args:
        es_sources: Elasticsearch源配置列表
        debug_mode: 是否启用调试模式
        prometheus_port: Prometheus服务器端口
        
    Returns:
        导出器实例
    """
    from .main import main_with_custom_config
    
    print("🚀 Quick starting OCPP 1.6 Multi-Source Exporter...")
    print_package_info()
    
    return main_with_custom_config(
        es_sources=es_sources,
        debug_mode=debug_mode,
        prometheus_port=prometheus_port
    )

# 包初始化完成消息
def _init_message():
    """包初始化消息"""
    return f"OCPP 1.6 Multi-Source Exporter Package v{__version__} initialized"

# 在导入时显示初始化消息（仅在调试模式下）
import os
if os.getenv('OCPP_EXPORTER_DEBUG', '').lower() in ('true', '1', 'yes'):
    print(_init_message())
