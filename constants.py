#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP 1.6 Standard Constants
定义OCPP 1.6标准中的所有常量，包括错误代码、充电桩状态、消息类型等
"""

# OCPP 1.6 Standard Error Codes
OCPP_ERROR_CODES = {
    'NoError': 0,
    'ConnectorLockFailure': 1,
    'EVCommunicationError': 2,
    'GroundFailure': 3,
    'HighTemperature': 4,
    'InternalError': 5,
    'LocalListConflict': 6,
    'OtherError': 7,
    'OverCurrentFailure': 8,
    'OverVoltage': 9,
    'PowerMeterFailure': 10,
    'PowerSwitchFailure': 11,
    'ReaderFailure': 12,
    'ResetFailure': 13,
    'UnderVoltage': 14,
    'WeakSignal': 15
}

# OCPP 1.6 ChargePointStatus
CHARGE_POINT_STATUS = {
    'Available': 0,
    'Preparing': 1,
    'Charging': 2,
    'SuspendedEVSE': 3,
    'SuspendedEV': 4,
    'Finishing': 5,
    'Reserved': 6,
    'Unavailable': 7,
    'Faulted': 8
}

# OCPP 1.6 Message Types (expanded based on actual data)
OCPP_MESSAGE_TYPES = [
    'StatusNotification',
    'Heartbeat',
    'BootNotification', 
    'StartTransaction',
    'StopTransaction',
    'MeterValues',
    'Authorize',
    'DataTransfer',
    'DiagnosticsStatusNotification',
    'FirmwareStatusNotification',
    'RemoteStartTransaction',
    'RemoteStopTransaction',
    'Reset',
    'ChangeConfiguration',
    'GetConfiguration',
    'ClearCache',
    'UnlockConnector'
]

# OCPP 1.6 Connector ID limits
CONNECTOR_ID_MIN = 0  # 0 = whole ChargePoint
CONNECTOR_ID_MAX = 255  # Maximum connector ID

# OCPP 1.6 ChargePoint ID limits
CHARGEPOINT_ID_MAX_LENGTH = 20  # Maximum length for ChargePoint ID

# Default Elasticsearch configuration
DEFAULT_ES_SOURCES = [
    {
        'name': 'primary',
        'host': '************',
        'port': 9200,
        'priority': 1
    },
    {
        'name': 'secondary', 
        'host': '*************',
        'port': 9200,
        'priority': 2
    }
]

# Default target index for OCPP data
DEFAULT_TARGET_INDEX = 'international_protocol'

# Default time range for data queries (from 2024-01-01 to 2025-08-31)
DEFAULT_TIME_RANGE_START = "2024-01-01T00:00:00"
DEFAULT_TIME_RANGE_END = "2025-08-31T23:59:59"

# Prometheus metrics configuration
PROMETHEUS_DEFAULT_PORT = 8000

# Scheduler intervals (in minutes)
SCHEDULER_INTERVALS = {
    'data_collection': 2,      # Every 2 minutes
    'message_type_discovery': 60,  # Every hour
    'transaction_pairing': 30  # Every 30 minutes
}

# Query limits
ELASTICSEARCH_QUERY_SIZE_LIMIT = 1000
ELASTICSEARCH_AGGREGATION_SIZE_LIMIT = 2000

# File output configuration
DEFAULT_OUTPUT_FILE_PREFIX = "ocpp_exporter_output"
