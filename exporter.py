#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP 1.6 Compliant Charging Station Exporter - Main Exporter Class
主导出器类，集成所有模块功能，提供统一的OCPP数据导出接口
"""

import json
import time
import logging
import threading
from datetime import datetime
from typing import Dict, List, Optional
from collections import defaultdict
import schedule

# 导入自定义模块
from constants import DEFAULT_ES_SOURCES, DEFAULT_TARGET_INDEX, SCHEDULER_INTERVALS
from file_output import FileOutputManager, create_timestamped_filename
from time_utils import calculate_time_range_hours, get_current_time_ms, get_time_range_ms, get_fixed_date_range_ms
from document_parser import create_parser
from elasticsearch_client import create_client
from transaction_analyzer import create_analyzer
from message_type_detector import create_detector
from metrics_manager import create_metrics_manager

logger = logging.getLogger(__name__)


class OCPP16ChargingStationExporter:
    """OCPP 1.6兼容充电桩导出器 - 多源增强版本"""

    def __init__(self, es_sources=None, debug_mode=True, output_file=None):
        """
        初始化OCPP导出器
        
        Args:
            es_sources: Elasticsearch源配置列表
            debug_mode: 启用详细调试输出
            output_file: 输出文件路径
        """
        # 初始化文件输出管理器
        if output_file is None:
            output_file = create_timestamped_filename()
        self.file_output = FileOutputManager(output_file)
        self.file_output.save_header(f"OCPP 1.6 Multi-Source Exporter Started - {datetime.now().isoformat()}")
        
        # 基本配置
        self.es_sources = es_sources or DEFAULT_ES_SOURCES
        self.target_index = DEFAULT_TARGET_INDEX
        self.debug_mode = debug_mode
        
        # 初始化各个组件
        self._init_components()
        
        # 缓存最新状态
        self.station_status_cache = {}
        self.last_update_time = datetime.now()
        
        logger.info("OCPP 1.6 Multi-Source Exporter initialized successfully")
        self.file_output.save_and_print("✅ OCPP 1.6 Multi-Source Exporter initialized successfully")
    
    def _init_components(self):
        """初始化所有组件"""
        try:
            # 初始化Elasticsearch客户端
            self.elasticsearch_client = create_client(
                es_sources=self.es_sources,
                target_index=self.target_index,
                debug_mode=self.debug_mode,
                file_output=self.file_output
            )
            
            # 初始化文档解析器
            self.document_parser = create_parser(
                debug_mode=self.debug_mode,
                file_output=self.file_output
            )
            
            # 初始化事务分析器
            self.transaction_analyzer = create_analyzer(
                debug_mode=self.debug_mode,
                file_output=self.file_output
            )
            
            # 初始化消息类型检测器
            self.message_type_detector = create_detector(
                debug_mode=self.debug_mode,
                file_output=self.file_output
            )
            
            # 初始化指标管理器
            self.metrics_manager = create_metrics_manager(
                debug_mode=self.debug_mode,
                file_output=self.file_output
            )
            
            self.file_output.save_and_print("✅ All components initialized successfully")
            
        except Exception as e:
            error_msg = f"Failed to initialize components: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(f"❌ {error_msg}")
            raise
    
    def _calculate_time_range_hours(self) -> int:
        """计算从2024年1月1日到2025年8月31日的小时数"""
        hours = calculate_time_range_hours()
        self.file_output.save_and_print(f"Calculated time range: {hours} hours")
        return hours
    
    def _fetch_ocpp_data(self, time_range_hours=None) -> List[Dict]:
        """
        从所有源获取OCPP数据，包含全面调试、去重和事务配对分析
        
        Args:
            time_range_hours: 时间范围小时数
            
        Returns:
            去重后的OCPP消息数据列表
        """
        # 计算时间范围（毫秒）
        if time_range_hours is None:
            # 使用固定的日期范围（2024-01-01 到 2025-08-31）
            start_time_ms, current_time_ms = get_fixed_date_range_ms()
            time_range_hours = self._calculate_time_range_hours()  # 仅用于日志显示
        else:
            # 使用指定的小时数（从当前时间向前推算）
            start_time_ms, current_time_ms = get_time_range_ms(time_range_hours)
        
        self.file_output.save_header("OCPP DATA FETCH OPERATION")
        search_msg = f"Searching for OCPP data from {self.elasticsearch_client.get_source_count()} sources in last {time_range_hours} hours"
        logger.info(search_msg)
        self.file_output.save_and_print(search_msg)
        
        # 创建查询
        query = self.elasticsearch_client.create_time_range_query(
            start_time_ms=start_time_ms,
            end_time_ms=current_time_ms,
            size=1000
        )
        
        query_msg = "Executing primary search for ALL OCPP message types across all sources..."
        logger.info(query_msg)
        self.file_output.save_and_print(query_msg)
        search_results = self.elasticsearch_client.search_all_elasticsearch_sources(query)
        
        all_results = []
        source_record_counts = {}
        
        # 处理每个源的结果
        for response, source_name in search_results:
            if not response or 'hits' not in response:
                continue
                
            hits = response['hits']['hits']
            total = response['hits']['total']
            
            if isinstance(total, dict):
                total_count = total.get('value', 0)
            else:
                total_count = total
            
            source_msg = f"Source {source_name}: Found {len(hits)} OCPP messages (total: {total_count})"
            logger.info(source_msg)
            self.file_output.save_and_print(source_msg)
            source_record_counts[source_name] = len(hits)
            
            # 打印Elasticsearch数据
            if hits:
                self.document_parser.print_elasticsearch_data(hits, f"Primary ALL Message Types Search Results", source_name)
                
                # 显示最新数据信息
                latest = hits[0]['_source']
                latest_time = latest.get('time', 0)
                from time_utils import convert_ms_to_datetime
                latest_datetime = convert_ms_to_datetime(latest_time)
                latest_msg = f"Source {source_name} latest message time: {latest_datetime} ({latest_time})"
                logger.info(latest_msg)
                self.file_output.save_and_print(latest_msg)
            
            # 解析所有文档
            parsed_count = 0
            for hit in hits:
                source_doc = hit['_source']
                parsed = self.document_parser.parse_document(source_doc, source_name)
                
                if parsed and parsed.get('identity'):
                    parsed['es_index'] = hit['_index']
                    parsed['es_source'] = source_name
                    all_results.append(parsed)
                    parsed_count += 1
            
            if self.debug_mode:
                parse_msg = f"DEBUG: Source {source_name} successfully parsed {parsed_count} out of {len(hits)} documents"
                self.file_output.save_and_print(parse_msg)
        
        # 去重结果
        deduped_results = self._deduplicate_records(all_results)
        
        # 执行事务配对分析
        pairing_analysis = self.transaction_analyzer.analyze_transaction_pairing(deduped_results, start_time_ms, current_time_ms)
        self.metrics_manager.update_transaction_pairing_metrics(pairing_analysis)
        
        # 更新发现指标
        unique_chargepoints = set(r.get('identity') for r in deduped_results if r.get('identity'))
        message_types = set(r.get('message_type') for r in deduped_results if r.get('message_type'))
        sources_with_data = set(r.get('data_source') for r in deduped_results if r.get('data_source'))
        
        # 更新源指标
        self.metrics_manager.update_source_metrics(self.elasticsearch_client.get_active_sources(), source_record_counts)
        
        # 更新发现指标
        self.metrics_manager.update_discovery_metrics(
            total_records_raw=len(all_results),
            total_records_deduplicated=len(deduped_results),
            unique_chargepoints=unique_chargepoints,
            message_types=message_types,
            sources_with_data=sources_with_data,
            source_record_counts=source_record_counts,
            time_range_hours=time_range_hours
        )
        
        # 打印处理摘要
        if self.debug_mode:
            self._print_processing_summary(all_results, deduped_results, unique_chargepoints, message_types, sources_with_data, source_record_counts)
        
        completion_msg = "Multi-source OCPP data collection completed:"
        logger.info(completion_msg)
        self.file_output.save_and_print(completion_msg)
        
        summary_stats = [
            f"  Raw records: {len(all_results)}, Deduplicated: {len(deduped_results)}",
            f"  Unique ChargePoints: {sorted(unique_chargepoints)}",
            f"  Sources: {sorted(sources_with_data)}",
            f"  Message types: {sorted(message_types)}"
        ]
        
        for stat in summary_stats:
            logger.info(stat)
            self.file_output.save_and_print(stat)
        
        return deduped_results
    
    def _deduplicate_records(self, records: List[Dict]) -> List[Dict]:
        """去重记录，保留每个ChargePoint-Connector组合的最新记录"""
        if not records:
            return []
        
        # 按ChargePoint ID + Connector ID + Message Type分组
        grouped = {}
        for record in records:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}_{record.get('message_type', '')}"
            doc_time = record.get('doc_time', 0)
            
            if key not in grouped or doc_time > grouped[key].get('doc_time', 0):
                grouped[key] = record
        
        deduped = list(grouped.values())
        
        if self.debug_mode:
            dedup_msg = f"DEBUG: Deduplication: {len(records)} -> {len(deduped)} records"
            self.file_output.save_and_print(dedup_msg)
        
        return deduped

    def _print_processing_summary(self, all_results: List[Dict], deduped_results: List[Dict],
                                 unique_chargepoints: set, message_types: set,
                                 sources_with_data: set, source_record_counts: Dict):
        """打印处理摘要"""
        if not self.file_output:
            return

        self.file_output.save_header("MULTI-SOURCE PROCESSING SUMMARY")
        self.file_output.save_and_print(f"  Total records retrieved: {len(all_results)} (before deduplication)")
        self.file_output.save_and_print(f"  Total records after deduplication: {len(deduped_results)}")
        self.file_output.save_and_print(f"  Sources with data: {sorted(sources_with_data)}")
        self.file_output.save_and_print(f"  Source record counts: {source_record_counts}")
        self.file_output.save_and_print(f"  Unique ChargePoints: {len(unique_chargepoints)}")
        self.file_output.save_and_print(f"  ChargePoint IDs: {sorted(unique_chargepoints)}")
        self.file_output.save_and_print(f"  Message types found: {sorted(message_types)}")

        # 打印去重结果样本
        if deduped_results:
            self.file_output.save_and_print(f"\nDEBUG: Sample Deduplicated Results (first 3):")
            for i, result in enumerate(deduped_results[:3]):
                from time_utils import convert_ms_to_datetime
                sample_msg = (f"  {i+1}. ID: {result.get('identity')}, Source: {result.get('data_source')}, "
                             f"Type: {result.get('message_type')}, Status: {result.get('status')}, "
                             f"Time: {convert_ms_to_datetime(result.get('doc_time', 0))}")
                self.file_output.save_and_print(sample_msg)

    def _update_metrics(self, data: List[Dict]):
        """更新Prometheus指标"""
        try:
            # 消息类型分析
            message_type_analysis = self.message_type_detector.analyze_message_types(data)
            self.metrics_manager.update_message_type_metrics(message_type_analysis)

            # 更新OCPP核心指标
            self.metrics_manager.update_ocpp_metrics(data, self.document_parser)

            self.last_update_time = datetime.now()

        except Exception as e:
            error_msg = f"Failed to update OCPP metrics: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)

    def collect_and_update(self):
        """收集OCPP数据并更新指标"""
        start_msg = "Starting OCPP 1.6 multi-source data collection..."
        logger.info(start_msg)
        self.file_output.save_and_print(start_msg)

        data = self._fetch_ocpp_data()

        if data:
            self._update_metrics(data)
            success_msg = "OCPP multi-source data collection and metrics update completed"
            logger.info(success_msg)
            self.file_output.save_and_print(success_msg)
        else:
            warning_msg = "No OCPP data retrieved from any source"
            logger.warning(warning_msg)
            self.file_output.save_and_print(warning_msg)

    def run_message_type_discovery(self):
        """运行全面的消息类型发现"""
        discovery_start_msg = "🔍 Running comprehensive MessageType discovery..."
        logger.info(discovery_start_msg)
        self.file_output.save_and_print(discovery_start_msg)

        analysis_result = self.message_type_detector.discover_all_message_types(self.elasticsearch_client)
        self.metrics_manager.update_message_type_metrics(analysis_result)

        discovery_completed_msg = "🔍 MessageType discovery completed"
        logger.info(discovery_completed_msg)
        self.file_output.save_and_print(discovery_completed_msg)

        return analysis_result

    def run_transaction_pairing_analysis(self, time_range_hours=24):
        """运行独立的事务配对分析（默认1天时间窗口）"""
        # 事务配对分析使用1天时间窗口，不使用默认的全量数据查询
        if time_range_hours is None:
            time_range_hours = 24  # 默认1天时间窗口，符合规范要求

        pairing_start_msg = f"🔄 Running standalone Transaction Pairing Analysis (range: {time_range_hours} hours)..."
        logger.info(pairing_start_msg)
        self.file_output.save_and_print(pairing_start_msg)

        data = self._fetch_ocpp_data(time_range_hours)
        if data:
            start_time_ms, current_time_ms = get_time_range_ms(time_range_hours)
            analysis_result = self.transaction_analyzer.analyze_transaction_pairing(data, start_time_ms, current_time_ms)
            self.metrics_manager.update_transaction_pairing_metrics(analysis_result)

            pairing_completed_msg = "🔄 Standalone Transaction Pairing Analysis completed"
            logger.info(pairing_completed_msg)
            self.file_output.save_and_print(pairing_completed_msg)

            return analysis_result
        else:
            no_data_msg = "No data available for Transaction Pairing Analysis"
            logger.warning(no_data_msg)
            self.file_output.save_and_print(no_data_msg)
            return {}

    def start_scheduler(self):
        """启动定时OCPP数据收集"""
        self.file_output.save_header("STARTING SCHEDULER OPERATIONS")

        # 初始操作
        self.collect_and_update()
        self.run_message_type_discovery()
        self.run_transaction_pairing_analysis()

        # 安排定期操作
        schedule.every(SCHEDULER_INTERVALS['data_collection']).minutes.do(self.collect_and_update)
        schedule.every(SCHEDULER_INTERVALS['message_type_discovery']).minutes.do(self.run_message_type_discovery)
        schedule.every(SCHEDULER_INTERVALS['transaction_pairing']).minutes.do(self.run_transaction_pairing_analysis)

        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(1)

        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()

        scheduler_msgs = [
            f"OCPP multi-source data collection scheduler started (every {SCHEDULER_INTERVALS['data_collection']} minutes) - {self.elasticsearch_client.get_source_count()} sources active",
            f"🔍 MessageType discovery scheduler started (every {SCHEDULER_INTERVALS['message_type_discovery']} minutes)",
            f"🔄 Transaction Pairing Analysis scheduler started (every {SCHEDULER_INTERVALS['transaction_pairing']} minutes)"
        ]

        for msg in scheduler_msgs:
            logger.info(msg)
            self.file_output.save_and_print(msg)

    def get_registry(self):
        """获取Prometheus指标注册表"""
        return self.metrics_manager.get_registry()

    def get_active_sources(self):
        """获取活跃的Elasticsearch源"""
        return self.elasticsearch_client.get_active_sources()

    def get_file_output_path(self):
        """获取文件输出路径"""
        return self.file_output.get_output_file()


def create_exporter(es_sources=None, debug_mode=True, output_file=None) -> OCPP16ChargingStationExporter:
    """
    创建OCPP导出器实例

    Args:
        es_sources: Elasticsearch源配置列表
        debug_mode: 是否启用调试模式
        output_file: 输出文件路径

    Returns:
        OCPP导出器实例
    """
    return OCPP16ChargingStationExporter(
        es_sources=es_sources,
        debug_mode=debug_mode,
        output_file=output_file
    )
