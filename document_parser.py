#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP Document Parser and Validator
OCPP文档解析和验证模块，负责解析、验证和标准化OCPP消息文档
"""

import json
import logging
from typing import Dict, Optional, Union
from constants import (
    OCPP_MESSAGE_TYPES, 
    CHARGE_POINT_STATUS, 
    OCPP_ERROR_CODES,
    CONNECTOR_ID_MIN,
    CONNECTOR_ID_MAX,
    CHARGEPOINT_ID_MAX_LENGTH
)

logger = logging.getLogger(__name__)


class OCPPDocumentParser:
    """OCPP文档解析器"""
    
    def __init__(self, debug_mode: bool = False, file_output=None):
        """
        初始化OCPP文档解析器
        
        Args:
            debug_mode: 是否启用调试模式
            file_output: 文件输出管理器实例
        """
        self.debug_mode = debug_mode
        self.file_output = file_output
    
    def parse_document(self, doc: Dict, source_name: str = "unknown") -> Optional[Dict]:
        """
        解析OCPP文档，提取和验证OCPP消息数据
        
        Args:
            doc: 原始文档字典
            source_name: 数据源名称
            
        Returns:
            解析后的OCPP消息字典，解析失败返回None
        """
        try:
            # 提取mess对象，包含OCPP数据
            mess = doc.get('mess', {})
            if not mess:
                if self.debug_mode and self.file_output:
                    debug_msg = f"DEBUG: No mess object found in document from {source_name}: {doc.get('_id', 'unknown')}"
                    self.file_output.save_and_print(debug_msg)
                return None
                
            # 验证消息类型
            message_type = mess.get('messType', '')
            if message_type not in OCPP_MESSAGE_TYPES and self.debug_mode and self.file_output:
                unknown_msg = f"DEBUG: Unknown OCPP message type from {source_name}: {message_type}"
                self.file_output.save_and_print(unknown_msg)
                
            # 解析嵌套的OCPP消息
            parsed = {
                'identity': mess.get('identity', ''),
                'message_type': message_type,
                'connector_id': self.validate_connector_id(mess.get('connectorId', 0)),
                'status': mess.get('status', ''),
                'error_code': mess.get('errorCode', 'NoError'),
                'vendor_id': mess.get('vendorId', ''),
                'vendor_error_code': mess.get('vendorErrorCode', ''),
                'timestamp': mess.get('timestamp', ''),
                'info': mess.get('info', ''),
                'unique_id': mess.get('uniqueId', ''),
                # 事务数据
                'transaction_id': mess.get('transactionId'),
                'meter_value': mess.get('meterValue'),
                'id_tag': mess.get('idTag', ''),
                # 文档级别字段
                'pile_code': doc.get('pileCode', ''),
                'doc_time': doc.get('time', 0),  # 毫秒时间戳
                'doc_type': doc.get('type', 0),
                'doc_mess_type': doc.get('messType', ''),
                # 数据源跟踪
                'data_source': source_name
            }
            
            # 使用pile_code作为identity的备用值
            if not parsed['identity'] and parsed['pile_code']:
                parsed['identity'] = parsed['pile_code']
                if self.debug_mode and self.file_output:
                    fallback_msg = f"DEBUG: Using pileCode as identity from {source_name}: {parsed['pile_code']}"
                    self.file_output.save_and_print(fallback_msg)
                
            # 验证ChargePoint ID格式
            if parsed['identity']:
                parsed['identity'] = self.normalize_chargepoint_id(parsed['identity'])
                
            # 调试打印解析数据
            if self.debug_mode and self.file_output and parsed['identity']:
                parsed_msg = f"DEBUG: Parsed from {source_name} - ID: {parsed['identity']}, Type: {parsed['message_type']}, Status: {parsed['status']}, Connector: {parsed['connector_id']}"
                self.file_output.save_and_print(parsed_msg)
                
            return parsed
            
        except Exception as e:
            error_msg = f"Failed to parse OCPP document from {source_name}: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
            if self.debug_mode and self.file_output:
                self.file_output.save_and_print(f"DEBUG: Parse error from {source_name} for document: {json.dumps(doc, indent=2)}")
            return None
    
    def validate_connector_id(self, connector_id: Union[int, str]) -> int:
        """
        验证和标准化连接器ID，符合OCPP 1.6规范
        
        Args:
            connector_id: 连接器ID
            
        Returns:
            验证后的连接器ID
        """
        try:
            conn_id = int(connector_id)
            # OCPP 1.6: connector_id 0 = 整个ChargePoint, 1+ = 单个连接器
            if conn_id < CONNECTOR_ID_MIN or conn_id > CONNECTOR_ID_MAX:
                if self.debug_mode and self.file_output:
                    invalid_msg = f"DEBUG: Invalid connector ID {conn_id}, setting to 0"
                    self.file_output.save_and_print(invalid_msg)
                return 0
            return conn_id
        except (ValueError, TypeError):
            if self.debug_mode and self.file_output:
                format_msg = f"DEBUG: Invalid connector ID format: {connector_id}"
                self.file_output.save_and_print(format_msg)
            return 0
    
    def normalize_chargepoint_id(self, chargepoint_id: str) -> str:
        """
        标准化ChargePoint ID，符合OCPP 1.6规范
        
        Args:
            chargepoint_id: ChargePoint ID
            
        Returns:
            标准化后的ChargePoint ID
        """
        # 移除无效字符并确保长度限制
        normalized = str(chargepoint_id).strip()
        if len(normalized) > CHARGEPOINT_ID_MAX_LENGTH:  # OCPP 1.6限制
            normalized = normalized[:CHARGEPOINT_ID_MAX_LENGTH]
            if self.debug_mode and self.file_output:
                truncate_msg = f"DEBUG: ChargePoint ID truncated to: {normalized}"
                self.file_output.save_and_print(truncate_msg)
        return normalized
    
    def get_status_value(self, status: str) -> int:
        """
        将OCPP 1.6状态字符串转换为数值
        
        Args:
            status: 状态字符串
            
        Returns:
            状态数值，未知状态返回-1
        """
        value = CHARGE_POINT_STATUS.get(status, -1)
        if value == -1 and self.debug_mode and self.file_output:
            unknown_status_msg = f"DEBUG: Unknown OCPP status: {status}"
            self.file_output.save_and_print(unknown_status_msg)
        return value
    
    def get_error_code_value(self, error_code: str) -> int:
        """
        将OCPP 1.6错误代码转换为数值
        
        Args:
            error_code: 错误代码字符串
            
        Returns:
            错误代码数值，未知错误代码返回-1
        """
        value = OCPP_ERROR_CODES.get(error_code, -1)
        if value == -1 and self.debug_mode and self.file_output:
            unknown_error_msg = f"DEBUG: Unknown OCPP error code: {error_code}"
            self.file_output.save_and_print(unknown_error_msg)
        return value
    
    def is_fault_status(self, status: str, error_code: str) -> bool:
        """
        根据OCPP 1.6规范检查状态是否表示故障
        
        Args:
            status: 状态字符串
            error_code: 错误代码字符串
            
        Returns:
            是否为故障状态
        """
        return status == 'Faulted' or error_code != 'NoError'
    
    def validate_message_type(self, message_type: str) -> bool:
        """
        验证消息类型是否为已知的OCPP消息类型
        
        Args:
            message_type: 消息类型字符串
            
        Returns:
            是否为有效的消息类型
        """
        return message_type in OCPP_MESSAGE_TYPES
    
    def extract_transaction_data(self, parsed_doc: Dict) -> Optional[Dict]:
        """
        从解析的文档中提取事务相关数据
        
        Args:
            parsed_doc: 解析后的文档
            
        Returns:
            事务数据字典，无事务数据返回None
        """
        if not parsed_doc or parsed_doc.get('message_type') not in ['StartTransaction', 'StopTransaction']:
            return None
        
        return {
            'chargepoint_id': parsed_doc.get('identity', ''),
            'connector_id': parsed_doc.get('connector_id', 0),
            'transaction_id': parsed_doc.get('transaction_id'),
            'message_type': parsed_doc.get('message_type'),
            'timestamp': parsed_doc.get('doc_time', 0),
            'id_tag': parsed_doc.get('id_tag', ''),
            'meter_value': parsed_doc.get('meter_value'),
            'data_source': parsed_doc.get('data_source', 'unknown')
        }
    
    def print_elasticsearch_data(self, hits: list, title: str = "Elasticsearch Data", source_name: str = "unknown"):
        """
        打印详细的Elasticsearch数据用于调试
        
        Args:
            hits: Elasticsearch命中结果列表
            title: 打印标题
            source_name: 数据源名称
        """
        if not self.debug_mode or not self.file_output:
            return
            
        self.file_output.save_header(f"DEBUG: {title} (Source: {source_name})", "=", 90)
        
        for i, hit in enumerate(hits[:5]):  # 打印前5条记录用于检查
            self.file_output.save_and_print(f"\n--- Record {i+1} from {source_name} ---")
            source = hit.get('_source', {})
            
            # 打印文档级别信息
            self.file_output.save_and_print(f"Document ID: {hit.get('_id', 'N/A')}")
            self.file_output.save_and_print(f"Index: {hit.get('_index', 'N/A')}")
            self.file_output.save_and_print(f"Score: {hit.get('_score', 'N/A')}")
            
            # 打印文档级别字段
            self.file_output.save_and_print(f"pileCode: {source.get('pileCode', 'N/A')}")
            self.file_output.save_and_print(f"time: {source.get('time', 'N/A')}")
            self.file_output.save_and_print(f"type: {source.get('type', 'N/A')}")
            self.file_output.save_and_print(f"messType (doc level): {source.get('messType', 'N/A')}")
            
            # 打印mess对象详情
            mess = source.get('mess', {})
            if mess:
                self.file_output.save_and_print(f"\nMess Object:")
                for key, value in mess.items():
                    self.file_output.save_and_print(f"  {key}: {value}")
            else:
                self.file_output.save_and_print("  No mess object found!")
            
            # 打印第一条记录的完整原始数据
            if i == 0:
                self.file_output.save_and_print(f"\nComplete Raw Data (Record 1 from {source_name}):")
                self.file_output.save_and_print(json.dumps(source, indent=2, ensure_ascii=False))
        
        if len(hits) > 5:
            self.file_output.save_and_print(f"\n... and {len(hits) - 5} more records from {source_name}")


def create_parser(debug_mode: bool = False, file_output=None) -> OCPPDocumentParser:
    """
    创建OCPP文档解析器实例
    
    Args:
        debug_mode: 是否启用调试模式
        file_output: 文件输出管理器实例
        
    Returns:
        OCPP文档解析器实例
    """
    return OCPPDocumentParser(debug_mode=debug_mode, file_output=file_output)
