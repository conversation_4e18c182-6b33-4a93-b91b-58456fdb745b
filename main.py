#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP 1.6 Multi-Source Exporter - Main Entry Point
程序入口点，包含主函数和配置
"""

import time
import logging
from datetime import datetime
from prometheus_client import start_http_server
from prometheus_client.core import REGISTRY

# 导入自定义模块
from constants import DEFAULT_ES_SOURCES, PROMETHEUS_DEFAULT_PORT
from file_output import create_timestamped_filename
from exporter import create_exporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """OCPP 1.6多源导出器主函数，包含事务配对分析和文件输出"""
    try:
        # 配置多个Elasticsearch源
        es_sources = [
            {
                'name': 'primary',
                'host': '************',
                'port': 9200,
                'priority': 1
            },
            {
                'name': 'secondary', 
                'host': '*************',
                'port': 9200,
                'priority': 2
            }
        ]
        
        # 创建带时间戳的输出文件
        output_filename = create_timestamped_filename()
        
        # 创建导出器，启用调试模式和文件输出
        exporter = create_exporter(
            es_sources=es_sources, 
            debug_mode=True,
            output_file=output_filename
        )
        
        # 清除现有注册表
        REGISTRY._collector_to_names.clear()
        REGISTRY._names_to_collectors.clear()
        
        # 启动数据收集调度器
        exporter.start_scheduler()
        
        # 启动Prometheus HTTP服务器
        start_http_server(PROMETHEUS_DEFAULT_PORT, registry=exporter.get_registry())
        
        startup_msgs = [
            f"OCPP 1.6 Multi-Source Prometheus exporter started, listening on port: {PROMETHEUS_DEFAULT_PORT}",
            f"Metrics endpoint: http://localhost:{PROMETHEUS_DEFAULT_PORT}/metrics",
            f"Active sources: {[s['name'] + '@' + s['host'] + ':' + str(s['port']) for s in exporter.get_active_sources()]}",
            "🔍 MessageType Detection Module enabled",
            "🔄 Transaction Pairing Analysis Module enabled",
            f"📄 All output saved to: {output_filename}"
        ]
        
        for msg in startup_msgs:
            logger.info(msg)
            print(msg)
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        shutdown_msg = "Received stop signal, shutting down OCPP multi-source exporter..."
        logger.info(shutdown_msg)
        print(shutdown_msg)
    except Exception as e:
        error_msg = f"OCPP multi-source exporter startup failed: {e}"
        logger.error(error_msg)
        print(error_msg)


def main_with_custom_config(es_sources=None, debug_mode=True, output_file=None, prometheus_port=None):
    """
    使用自定义配置运行OCPP导出器
    
    Args:
        es_sources: Elasticsearch源配置列表
        debug_mode: 是否启用调试模式
        output_file: 输出文件路径
        prometheus_port: Prometheus服务器端口
    """
    try:
        # 使用默认配置或自定义配置
        es_sources = es_sources or DEFAULT_ES_SOURCES
        prometheus_port = prometheus_port or PROMETHEUS_DEFAULT_PORT
        
        if output_file is None:
            output_file = create_timestamped_filename()
        
        # 创建导出器
        exporter = create_exporter(
            es_sources=es_sources,
            debug_mode=debug_mode,
            output_file=output_file
        )
        
        # 清除现有注册表
        REGISTRY._collector_to_names.clear()
        REGISTRY._names_to_collectors.clear()
        
        # 启动数据收集调度器
        exporter.start_scheduler()
        
        # 启动Prometheus HTTP服务器
        start_http_server(prometheus_port, registry=exporter.get_registry())
        
        startup_msgs = [
            f"OCPP 1.6 Multi-Source Prometheus exporter started with custom config",
            f"Listening on port: {prometheus_port}",
            f"Metrics endpoint: http://localhost:{prometheus_port}/metrics",
            f"Active sources: {len(exporter.get_active_sources())}",
            f"Debug mode: {debug_mode}",
            f"Output file: {output_file}"
        ]
        
        for msg in startup_msgs:
            logger.info(msg)
            print(msg)
        
        return exporter
        
    except Exception as e:
        error_msg = f"OCPP exporter startup failed: {e}"
        logger.error(error_msg)
        print(error_msg)
        raise


def run_single_collection(es_sources=None, debug_mode=True, output_file=None):
    """
    运行单次数据收集（用于测试）
    
    Args:
        es_sources: Elasticsearch源配置列表
        debug_mode: 是否启用调试模式
        output_file: 输出文件路径
        
    Returns:
        导出器实例
    """
    try:
        # 使用默认配置或自定义配置
        es_sources = es_sources or DEFAULT_ES_SOURCES
        
        if output_file is None:
            output_file = create_timestamped_filename("ocpp_single_collection")
        
        # 创建导出器
        exporter = create_exporter(
            es_sources=es_sources,
            debug_mode=debug_mode,
            output_file=output_file
        )
        
        print(f"Running single OCPP data collection...")
        print(f"Output will be saved to: {output_file}")
        
        # 运行单次收集
        exporter.collect_and_update()
        exporter.run_message_type_discovery()
        exporter.run_transaction_pairing_analysis()
        
        print("Single collection completed successfully!")
        return exporter
        
    except Exception as e:
        error_msg = f"Single collection failed: {e}"
        logger.error(error_msg)
        print(error_msg)
        raise


def run_analysis_only(es_sources=None, time_range_hours=None, output_file=None):
    """
    仅运行分析功能（消息类型发现和事务配对分析）
    
    Args:
        es_sources: Elasticsearch源配置列表
        time_range_hours: 时间范围小时数
        output_file: 输出文件路径
        
    Returns:
        分析结果字典
    """
    try:
        # 使用默认配置或自定义配置
        es_sources = es_sources or DEFAULT_ES_SOURCES
        
        if output_file is None:
            output_file = create_timestamped_filename("ocpp_analysis_only")
        
        # 创建导出器
        exporter = create_exporter(
            es_sources=es_sources,
            debug_mode=True,
            output_file=output_file
        )
        
        print(f"Running OCPP analysis only...")
        print(f"Output will be saved to: {output_file}")
        
        # 运行分析
        message_type_result = exporter.run_message_type_discovery()
        transaction_result = exporter.run_transaction_pairing_analysis(time_range_hours)
        
        analysis_results = {
            'message_type_analysis': message_type_result,
            'transaction_pairing_analysis': transaction_result,
            'analysis_time': datetime.now().isoformat(),
            'output_file': output_file
        }
        
        print("Analysis completed successfully!")
        return analysis_results
        
    except Exception as e:
        error_msg = f"Analysis failed: {e}"
        logger.error(error_msg)
        print(error_msg)
        raise


if __name__ == "__main__":
    main()
