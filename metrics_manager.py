#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prometheus Metrics Manager
Prometheus指标管理模块，负责初始化和更新所有OCPP相关的Prometheus指标
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from collections import defaultdict
from prometheus_client import CollectorRegistry, Gauge, Counter as PrometheusCounter, Info
from constants import OCPP_MESSAGE_TYPES
from time_utils import convert_ms_to_datetime

logger = logging.getLogger(__name__)


class MetricsManager:
    """Prometheus指标管理器"""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None, debug_mode: bool = False, file_output=None):
        """
        初始化指标管理器
        
        Args:
            registry: Prometheus注册表，None则创建新的
            debug_mode: 是否启用调试模式
            file_output: 文件输出管理器实例
        """
        self.registry = registry or CollectorRegistry()
        self.debug_mode = debug_mode
        self.file_output = file_output
        
        # 初始化所有指标
        self._init_metrics()
        
        if self.file_output:
            self.file_output.save_and_print("✅ Prometheus metrics initialized successfully")
    
    def _init_metrics(self):
        """初始化OCPP 1.6兼容的Prometheus指标"""
        if self.file_output:
            self.file_output.save_and_print("Initializing Prometheus metrics...")
        
        # 核心状态指标
        self.station_status = Gauge(
            'ocpp_chargepoint_status',
            'OCPP 1.6 ChargePoint status (0=Available,1=Preparing,2=Charging,3=SuspendedEVSE,4=SuspendedEV,5=Finishing,6=Reserved,7=Unavailable,8=Faulted)',
            ['chargepoint_id', 'connector_id', 'vendor_id', 'data_source'],
            registry=self.registry
        )
        
        # 增强的故障状态与OCPP错误代码
        self.station_fault_status = Gauge(
            'ocpp_chargepoint_fault',
            'OCPP 1.6 ChargePoint fault status (0=normal, 1=fault)',
            ['chargepoint_id', 'connector_id', 'error_code', 'vendor_error_code', 'data_source'],
            registry=self.registry
        )
        
        # 错误代码指标
        self.error_code_value = Gauge(
            'ocpp_error_code',
            'OCPP 1.6 Error code numeric value',
            ['chargepoint_id', 'connector_id', 'error_code', 'data_source'],
            registry=self.registry
        )
        
        # 事务指标
        self.active_transactions = Gauge(
            'ocpp_active_transactions',
            'Number of active charging transactions',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # OCPP消息计数器
        self.ocpp_messages_total = PrometheusCounter(
            'ocpp_messages_total',
            'Total OCPP messages processed',
            ['chargepoint_id', 'message_type', 'direction', 'data_source'],
            registry=self.registry
        )
        
        # 故障计数器
        self.fault_total = PrometheusCounter(
            'ocpp_faults_total',
            'Total OCPP faults by error code',
            ['chargepoint_id', 'connector_id', 'error_code', 'data_source'],
            registry=self.registry
        )
        
        # 心跳跟踪
        self.last_heartbeat = Gauge(
            'ocpp_last_heartbeat_timestamp',
            'Last heartbeat timestamp (Unix time)',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # 数据时间戳
        self.last_update = Gauge(
            'ocpp_last_update_timestamp',
            'Last data update timestamp (Unix time)',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # ChargePoint信息
        self.chargepoint_info = Info(
            'ocpp_chargepoint_info',
            'OCPP 1.6 ChargePoint information',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # 数据发现指标
        self.data_discovery = Info(
            'ocpp_data_discovery',
            'OCPP data discovery information from all sources',
            registry=self.registry
        )
        
        # 多源指标
        self.source_status = Gauge(
            'ocpp_data_source_status',
            'Data source availability status (1=active, 0=inactive)',
            ['source_name', 'source_host', 'source_port'],
            registry=self.registry
        )
        
        self.source_records_count = Gauge(
            'ocpp_data_source_records',
            'Number of records retrieved from each data source',
            ['source_name', 'source_host'],
            registry=self.registry
        )
        
        # 消息类型检测指标
        self.message_type_discovered = PrometheusCounter(
            'ocpp_message_types_discovered_total',
            'Total number of distinct message types discovered',
            ['data_source'],
            registry=self.registry
        )
        
        self.message_type_count = Gauge(
            'ocpp_message_type_count',
            'Count of messages by message type discovered from data',
            ['message_type', 'data_source', 'predefined'],
            registry=self.registry
        )
        
        self.message_type_discovery = Info(
            'ocpp_message_type_discovery',
            'OCPP message type discovery information',
            registry=self.registry
        )
        
        self.unknown_message_types = PrometheusCounter(
            'ocpp_unknown_message_types_total',
            'Count of unknown/undocumented message types discovered',
            ['message_type', 'data_source'],
            registry=self.registry
        )

        # 事务配对分析指标
        self.unpaired_start_transactions = Gauge(
            'ocpp_unpaired_start_transactions',
            'Number of StartTransaction messages without matching StopTransaction',
            ['chargepoint_id', 'connector_id', 'data_source'],
            registry=self.registry
        )
        
        self.unpaired_stop_transactions = Gauge(
            'ocpp_unpaired_stop_transactions', 
            'Number of StopTransaction messages without matching StartTransaction',
            ['chargepoint_id', 'connector_id', 'data_source'],
            registry=self.registry
        )
        
        self.transaction_pairing_info = Info(
            'ocpp_transaction_pairing_analysis',
            'OCPP transaction pairing analysis information',
            registry=self.registry
        )
        
        self.query_time_window = Info(
            'ocpp_query_time_window',
            'Current query time window information',
            registry=self.registry
        )
    
    def update_source_metrics(self, active_sources: List[Dict], source_record_counts: Dict):
        """
        更新数据源相关指标
        
        Args:
            active_sources: 活跃数据源列表
            source_record_counts: 各源记录数统计
        """
        try:
            # 更新源状态指标
            for source_config in active_sources:
                self.source_status.labels(
                    source_name=source_config['name'],
                    source_host=source_config['host'],
                    source_port=str(source_config['port'])
                ).set(1)
                
                # 更新记录数指标
                record_count = source_record_counts.get(source_config['name'], 0)
                self.source_records_count.labels(
                    source_name=source_config['name'],
                    source_host=source_config['host']
                ).set(record_count)
            
            if self.debug_mode and self.file_output:
                self.file_output.save_and_print(f"✅ Updated source metrics for {len(active_sources)} sources")
                
        except Exception as e:
            error_msg = f"❌ Failed to update source metrics: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
    
    def update_discovery_metrics(self, total_records_raw: int, total_records_deduplicated: int,
                                unique_chargepoints: set, message_types: set, sources_with_data: set,
                                source_record_counts: Dict, time_range_hours: int):
        """
        更新数据发现指标
        
        Args:
            total_records_raw: 原始记录总数
            total_records_deduplicated: 去重后记录总数
            unique_chargepoints: 唯一充电桩集合
            message_types: 消息类型集合
            sources_with_data: 有数据的源集合
            source_record_counts: 源记录数统计
            time_range_hours: 时间范围小时数
        """
        try:
            import json
            
            self.data_discovery.info({
                'total_records_raw': str(total_records_raw),
                'total_records_deduplicated': str(total_records_deduplicated),
                'unique_chargepoints': str(len(unique_chargepoints)),
                'chargepoints_found': ','.join(sorted(unique_chargepoints)) if unique_chargepoints else 'none',
                'message_types_found': ','.join(sorted(message_types)) if message_types else 'none',
                'active_sources': ','.join(sorted(sources_with_data)) if sources_with_data else 'none',
                'source_record_counts': json.dumps(source_record_counts),
                'search_time_range_hours': str(time_range_hours),
                'last_search_time': datetime.now().isoformat()
            })
            
            if self.debug_mode and self.file_output:
                self.file_output.save_and_print(f"✅ Updated discovery metrics")
                
        except Exception as e:
            error_msg = f"❌ Failed to update discovery metrics: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
    
    def update_message_type_metrics(self, analysis_result: Dict):
        """
        更新消息类型检测指标

        Args:
            analysis_result: 消息类型分析结果
        """
        if not analysis_result:
            return

        try:
            logger.info("🔍 Updating MessageType detection metrics...")
            if self.file_output:
                self.file_output.save_and_print("🔍 Updating MessageType detection metrics...")

            # 更新消息类型发现信息
            self.message_type_discovery.info({
                'total_discovered_types': str(analysis_result['total_discovered']),
                'discovered_types': ','.join(analysis_result['discovered_types']),
                'unknown_types': ','.join(analysis_result['unknown_types']),
                'missing_predefined_types': ','.join(analysis_result['missing_predefined']),
                'predefined_types_found': str(len(set(analysis_result['discovered_types']) & set(analysis_result['predefined_types']))),
                'last_scan_time': analysis_result['scan_time']
            })

            # 更新消息类型计数
            for message_type, count in analysis_result['type_counts'].items():
                for source, source_types in analysis_result['source_type_mapping'].items():
                    if message_type in source_types:
                        is_predefined = str(message_type in OCPP_MESSAGE_TYPES).lower()
                        self.message_type_count.labels(
                            message_type=message_type,
                            data_source=source,
                            predefined=is_predefined
                        ).set(count)

            # 更新每个源的发现计数器
            for source, types in analysis_result['source_type_mapping'].items():
                self.message_type_discovered.labels(data_source=source).inc(len(types))

            # 更新未知消息类型计数器
            for unknown_type in analysis_result['unknown_types']:
                for source, source_types in analysis_result['source_type_mapping'].items():
                    if unknown_type in source_types:
                        self.unknown_message_types.labels(
                            message_type=unknown_type,
                            data_source=source
                        ).inc()

            success_msg = f"✅ MessageType metrics updated: {analysis_result['total_discovered']} types tracked"
            logger.info(success_msg)
            if self.file_output:
                self.file_output.save_and_print(success_msg)

        except Exception as e:
            error_msg = f"❌ Failed to update MessageType metrics: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
            if self.debug_mode and self.file_output:
                import traceback
                self.file_output.save_and_print("DEBUG: MessageType metrics error traceback:")
                self.file_output.save_and_print(traceback.format_exc())

    def update_transaction_pairing_metrics(self, analysis_result: Dict):
        """
        更新事务配对分析指标

        Args:
            analysis_result: 事务配对分析结果
        """
        if not analysis_result:
            return

        try:
            logger.info("🔄 Updating Transaction Pairing metrics...")
            if self.file_output:
                self.file_output.save_and_print("🔄 Updating Transaction Pairing metrics...")

            # 更新时间窗口信息
            self.query_time_window.info({
                'start_time_ms': str(analysis_result['query_start_time_ms']),
                'end_time_ms': str(analysis_result['query_end_time_ms']),
                'start_datetime': analysis_result['query_start_datetime'],
                'end_datetime': analysis_result['query_end_datetime'],
                'window_duration_hours': str((analysis_result['query_end_time_ms'] - analysis_result['query_start_time_ms']) / (1000 * 60 * 60))
            })

            # 更新事务配对分析信息
            self.transaction_pairing_info.info({
                'total_start_transactions': str(analysis_result['total_start_transactions']),
                'total_stop_transactions': str(analysis_result['total_stop_transactions']),
                'transaction_difference': str(analysis_result['total_stop_transactions'] - analysis_result['total_start_transactions']),
                'chargepoints_with_unpaired_starts': str(len(analysis_result['unpaired_starts'])),
                'chargepoints_with_unpaired_stops': str(len(analysis_result['unpaired_stops'])),
                'total_chargepoints_with_unpaired': str(analysis_result['chargepoints_with_unpaired']),
                'analysis_time': analysis_result['analysis_time']
            })

            # 更新未配对开始事务指标
            for cp_conn, info in analysis_result['unpaired_starts'].items():
                for data_source in info['data_sources']:
                    self.unpaired_start_transactions.labels(
                        chargepoint_id=info['chargepoint_id'],
                        connector_id=info['connector_id'],
                        data_source=data_source
                    ).set(info['unpaired_count'])

            # 更新未配对停止事务指标
            for cp_conn, info in analysis_result['unpaired_stops'].items():
                for data_source in info['data_sources']:
                    self.unpaired_stop_transactions.labels(
                        chargepoint_id=info['chargepoint_id'],
                        connector_id=info['connector_id'],
                        data_source=data_source
                    ).set(info['unpaired_count'])

            success_msg = f"✅ Transaction Pairing metrics updated: {analysis_result['chargepoints_with_unpaired']} ChargePoints with unpaired transactions"
            logger.info(success_msg)
            if self.file_output:
                self.file_output.save_and_print(success_msg)

        except Exception as e:
            error_msg = f"❌ Failed to update Transaction Pairing metrics: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
            if self.debug_mode and self.file_output:
                import traceback
                traceback_msg = "DEBUG: Transaction Pairing metrics error traceback:"
                self.file_output.save_and_print(traceback_msg)
                self.file_output.save_and_print(traceback.format_exc())

    def update_ocpp_metrics(self, data: List[Dict], document_parser):
        """
        更新OCPP核心指标

        Args:
            data: 解析后的OCPP消息数据列表
            document_parser: 文档解析器实例
        """
        try:
            if self.debug_mode and self.file_output:
                metrics_start_msg = f"\nDEBUG: Starting metrics update with {len(data)} records from multiple sources"
                self.file_output.save_and_print(metrics_start_msg)

            # 按chargepoint+connector分组获取最新状态
            chargepoint_latest_status = {}
            heartbeat_times = {}
            active_transactions = {}
            source_tracking = {}

            for record in data:
                chargepoint_id = record.get('identity', '')
                if not chargepoint_id:
                    continue

                connector_id = str(record.get('connector_id', 0))
                doc_time = record.get('doc_time', 0)
                message_type = record.get('message_type', '')
                data_source = record.get('data_source', 'unknown')

                # 跟踪哪些源有每个chargepoint的数据
                if chargepoint_id not in source_tracking:
                    source_tracking[chargepoint_id] = set()
                source_tracking[chargepoint_id].add(data_source)

                # 更新消息计数器
                direction = "from_chargepoint"
                self.ocpp_messages_total.labels(
                    chargepoint_id=chargepoint_id,
                    message_type=message_type,
                    direction=direction,
                    data_source=data_source
                ).inc()

                # 处理StatusNotification消息
                if message_type == 'StatusNotification':
                    key = f"{chargepoint_id}_{connector_id}"

                    if key not in chargepoint_latest_status or doc_time > chargepoint_latest_status[key].get('doc_time', 0):
                        chargepoint_latest_status[key] = record
                        if self.debug_mode and self.file_output:
                            status_update_msg = f"DEBUG: Updated latest status for {key} from {data_source}: {record.get('status')}"
                            self.file_output.save_and_print(status_update_msg)

                # 处理Heartbeat消息
                elif message_type == 'Heartbeat':
                    hb_key = f"{chargepoint_id}_{data_source}"
                    if hb_key not in heartbeat_times or doc_time > heartbeat_times[hb_key]:
                        heartbeat_times[hb_key] = (doc_time, data_source, chargepoint_id)
                        if self.debug_mode and self.file_output:
                            hb_update_msg = f"DEBUG: Updated heartbeat for {chargepoint_id} from {data_source}: {convert_ms_to_datetime(doc_time)}"
                            self.file_output.save_and_print(hb_update_msg)

                # 处理事务消息
                elif message_type in ['StartTransaction', 'StopTransaction']:
                    tx_key = f"{chargepoint_id}_{data_source}"
                    if tx_key not in active_transactions:
                        active_transactions[tx_key] = 0

                    if message_type == 'StartTransaction':
                        active_transactions[tx_key] += 1
                    elif message_type == 'StopTransaction':
                        active_transactions[tx_key] = max(0, active_transactions[tx_key] - 1)

                    if self.debug_mode and self.file_output:
                        tx_update_msg = f"DEBUG: Transaction update for {chargepoint_id} from {data_source}: {active_transactions[tx_key]} active"
                        self.file_output.save_and_print(tx_update_msg)

            # 更新状态指标
            self._update_status_metrics(chargepoint_latest_status, source_tracking, document_parser)

            # 更新心跳指标
            self._update_heartbeat_metrics(heartbeat_times)

            # 更新事务指标
            self._update_transaction_metrics(active_transactions)

            metrics_completed_msg = f"Updated OCPP metrics for {len(chargepoint_latest_status)} ChargePoint-Connector pairs"
            logger.info(metrics_completed_msg)
            if self.file_output:
                self.file_output.save_and_print(metrics_completed_msg)

        except Exception as e:
            error_msg = f"Failed to update OCPP metrics: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
            if self.debug_mode and self.file_output:
                import traceback
                self.file_output.save_and_print("DEBUG: Multi-source metrics update error traceback:")
                self.file_output.save_and_print(traceback.format_exc())

    def _update_status_metrics(self, chargepoint_latest_status: Dict, source_tracking: Dict, document_parser):
        """更新状态相关指标"""
        updated_count = 0
        for key, record in chargepoint_latest_status.items():
            chargepoint_id = record.get('identity', '')
            connector_id = str(record.get('connector_id', 0))
            vendor_id = record.get('vendor_id', '')
            error_code = record.get('error_code', 'NoError')
            vendor_error_code = record.get('vendor_error_code', '')
            status = record.get('status', '')
            doc_time = record.get('doc_time', 0)
            data_source = record.get('data_source', 'unknown')

            status_time = convert_ms_to_datetime(doc_time)

            update_msg = f"Updating {chargepoint_id} connector {connector_id} from {data_source}: {status} at {status_time}"
            logger.info(update_msg)
            if self.file_output:
                self.file_output.save_and_print(update_msg)

            # 更新状态值
            status_value = document_parser.get_status_value(status)
            if status_value >= 0:
                self.station_status.labels(
                    chargepoint_id=chargepoint_id,
                    connector_id=connector_id,
                    vendor_id=vendor_id,
                    data_source=data_source
                ).set(status_value)

            # 更新错误代码指标
            error_code_value = document_parser.get_error_code_value(error_code)
            if error_code_value >= 0:
                self.error_code_value.labels(
                    chargepoint_id=chargepoint_id,
                    connector_id=connector_id,
                    error_code=error_code,
                    data_source=data_source
                ).set(error_code_value)

            # 更新故障状态
            is_fault = 1 if document_parser.is_fault_status(status, error_code) else 0
            self.station_fault_status.labels(
                chargepoint_id=chargepoint_id,
                connector_id=connector_id,
                error_code=error_code,
                vendor_error_code=vendor_error_code,
                data_source=data_source
            ).set(is_fault)

            # 记录和计数故障
            if is_fault:
                fault_msg = f"OCPP FAULT from {data_source}: {chargepoint_id} connector {connector_id} - {error_code}"
                logger.warning(fault_msg)
                if self.file_output:
                    self.file_output.save_and_print(fault_msg)
                self.fault_total.labels(
                    chargepoint_id=chargepoint_id,
                    connector_id=connector_id,
                    error_code=error_code,
                    data_source=data_source
                ).inc()

            # 更新最后更新时间
            self.last_update.labels(
                chargepoint_id=chargepoint_id,
                data_source=data_source
            ).set(doc_time / 1000)

            # 更新ChargePoint信息
            sources_for_cp = ','.join(sorted(source_tracking.get(chargepoint_id, set())))
            self.chargepoint_info.labels(
                chargepoint_id=chargepoint_id,
                data_source=data_source
            ).info({
                'vendor_id': vendor_id,
                'last_status': status,
                'last_error_code': error_code,
                'last_status_time': status_time,
                'ocpp_version': '1.6',
                'all_sources': sources_for_cp
            })

            updated_count += 1

        return updated_count

    def _update_heartbeat_metrics(self, heartbeat_times: Dict):
        """更新心跳相关指标"""
        for hb_key, (heartbeat_time, data_source, chargepoint_id) in heartbeat_times.items():
            self.last_heartbeat.labels(
                chargepoint_id=chargepoint_id,
                data_source=data_source
            ).set(heartbeat_time / 1000)

    def _update_transaction_metrics(self, active_transactions: Dict):
        """更新事务相关指标"""
        for tx_key, tx_count in active_transactions.items():
            chargepoint_id, data_source = tx_key.rsplit('_', 1)
            self.active_transactions.labels(
                chargepoint_id=chargepoint_id,
                data_source=data_source
            ).set(tx_count)

    def get_registry(self) -> CollectorRegistry:
        """获取Prometheus注册表"""
        return self.registry


def create_metrics_manager(registry: Optional[CollectorRegistry] = None, debug_mode: bool = False,
                          file_output=None) -> MetricsManager:
    """
    创建指标管理器实例

    Args:
        registry: Prometheus注册表
        debug_mode: 是否启用调试模式
        file_output: 文件输出管理器实例

    Returns:
        指标管理器实例
    """
    return MetricsManager(registry=registry, debug_mode=debug_mode, file_output=file_output)
