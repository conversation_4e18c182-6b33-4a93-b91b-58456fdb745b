#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Elasticsearch Multi-Source Client
多源Elasticsearch搜索客户端，负责连接测试、查询执行等功能
"""

import json
import logging
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from constants import DEFAULT_ES_SOURCES, DEFAULT_TARGET_INDEX

logger = logging.getLogger(__name__)


class ElasticsearchClient:
    """多源Elasticsearch客户端"""
    
    def __init__(self, es_sources: Optional[List[Dict]] = None, target_index: str = DEFAULT_TARGET_INDEX, 
                 debug_mode: bool = False, file_output=None):
        """
        初始化Elasticsearch客户端
        
        Args:
            es_sources: Elasticsearch源配置列表
            target_index: 目标索引名称
            debug_mode: 是否启用调试模式
            file_output: 文件输出管理器实例
        """
        self.es_sources = es_sources or DEFAULT_ES_SOURCES
        self.target_index = target_index
        self.debug_mode = debug_mode
        self.file_output = file_output
        self.active_sources = []
        
        # 测试所有ES连接
        self._test_elasticsearch_connections()
        
        if not self.active_sources:
            error_msg = "No active Elasticsearch sources available"
            if self.file_output:
                self.file_output.save_and_print(f"ERROR: {error_msg}")
            raise Exception(error_msg)
    
    def _test_elasticsearch_connections(self):
        """测试所有配置的Elasticsearch源的连接"""
        if self.file_output:
            self.file_output.save_header("Testing Elasticsearch Connections")
        
        for source_config in self.es_sources:
            try:
                es_url = f"http://{source_config['host']}:{source_config['port']}"
                response = requests.get(f'{es_url}/', timeout=10)
                
                if response.status_code == 200:
                    info = response.json()
                    source_config['url'] = es_url
                    source_config['cluster_name'] = info['cluster_name']
                    source_config['version'] = info['version']['number']
                    
                    self.active_sources.append(source_config)
                    
                    success_msg = (f"✅ Connected to ES source '{source_config['name']}': "
                                  f"{source_config['host']}:{source_config['port']} "
                                  f"(cluster: {info['cluster_name']}, version: {info['version']['number']})")
                    logger.info(success_msg)
                    if self.file_output:
                        self.file_output.save_and_print(success_msg)
                else:
                    warning_msg = f"⚠️ ES source '{source_config['name']}' returned HTTP {response.status_code}"
                    logger.warning(warning_msg)
                    if self.file_output:
                        self.file_output.save_and_print(warning_msg)
                        
            except Exception as e:
                error_msg = (f"❌ Failed to connect to ES source '{source_config['name']}' "
                           f"({source_config['host']}:{source_config['port']}): {e}")
                logger.error(error_msg)
                if self.file_output:
                    self.file_output.save_and_print(error_msg)
        
        summary_msg = f"Active Elasticsearch sources: {len(self.active_sources)}/{len(self.es_sources)}"
        logger.info(summary_msg)
        if self.file_output:
            self.file_output.save_and_print(summary_msg)
    
    def search_elasticsearch_source(self, source_config: Dict, query: Dict) -> Tuple[Dict, str]:
        """
        搜索单个Elasticsearch源，增强错误处理和调试
        
        Args:
            source_config: 源配置字典
            query: 查询字典
            
        Returns:
            (搜索结果, 源名称) 的元组
        """
        source_name = source_config['name']
        try:
            url = f"{source_config['url']}/{self.target_index}/_search"
            headers = {'Content-Type': 'application/json'}
            
            if self.debug_mode and self.file_output:
                self.file_output.save_and_print(f"\nDEBUG: Elasticsearch Query for {source_name}:")
                self.file_output.save_and_print(json.dumps(query, indent=2))
                self.file_output.save_and_print(f"URL: {url}")
            
            response = requests.post(url, json=query, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if self.debug_mode and self.file_output:
                    total = result.get('hits', {}).get('total', 0)
                    if isinstance(total, dict):
                        total_count = total.get('value', 0)
                    else:
                        total_count = total
                    debug_response_msg = f"DEBUG: Source {source_name} returned {total_count} total hits"
                    self.file_output.save_and_print(debug_response_msg)
                return result, source_name
            else:
                error_msg = f"ES search failed for {source_name}: HTTP {response.status_code}"
                logger.error(error_msg)
                if self.file_output:
                    self.file_output.save_and_print(error_msg)
                if self.debug_mode and self.file_output:
                    self.file_output.save_and_print(f"DEBUG: ES Response from {source_name}: {response.text}")
                return {}, source_name
                
        except Exception as e:
            error_msg = f"ES search request failed for {source_name}: {e}"
            logger.error(error_msg)
            if self.file_output:
                self.file_output.save_and_print(error_msg)
            return {}, source_name
    
    def search_all_elasticsearch_sources(self, query: Dict) -> List[Tuple[Dict, str]]:
        """
        并发搜索所有活跃的Elasticsearch源
        
        Args:
            query: 查询字典
            
        Returns:
            (搜索结果, 源名称) 元组的列表
        """
        results = []
        
        # 使用ThreadPoolExecutor进行并发搜索
        with ThreadPoolExecutor(max_workers=len(self.active_sources)) as executor:
            # 提交所有搜索任务
            future_to_source = {
                executor.submit(self.search_elasticsearch_source, source_config, query): source_config
                for source_config in self.active_sources
            }
            
            # 收集完成的结果
            for future in as_completed(future_to_source):
                source_config = future_to_source[future]
                try:
                    result, source_name = future.result()
                    if result:
                        results.append((result, source_name))
                        success_msg = f"✅ Retrieved data from source: {source_name}"
                        logger.info(success_msg)
                        if self.file_output:
                            self.file_output.save_and_print(success_msg)
                    else:
                        warning_msg = f"⚠️ No data from source: {source_name}"
                        logger.warning(warning_msg)
                        if self.file_output:
                            self.file_output.save_and_print(warning_msg)
                except Exception as e:
                    error_msg = f"Search failed for source {source_config['name']}: {e}"
                    logger.error(error_msg)
                    if self.file_output:
                        self.file_output.save_and_print(error_msg)
        
        return results
    
    def get_active_sources(self) -> List[Dict]:
        """获取活跃的Elasticsearch源列表"""
        return self.active_sources
    
    def get_source_count(self) -> int:
        """获取活跃源的数量"""
        return len(self.active_sources)
    
    def test_connection(self, source_config: Dict) -> bool:
        """
        测试单个Elasticsearch源的连接
        
        Args:
            source_config: 源配置字典
            
        Returns:
            连接是否成功
        """
        try:
            es_url = f"http://{source_config['host']}:{source_config['port']}"
            response = requests.get(f'{es_url}/', timeout=10)
            return response.status_code == 200
        except Exception:
            return False
    
    def get_cluster_info(self, source_config: Dict) -> Optional[Dict]:
        """
        获取Elasticsearch集群信息
        
        Args:
            source_config: 源配置字典
            
        Returns:
            集群信息字典，连接失败返回None
        """
        try:
            es_url = f"http://{source_config['host']}:{source_config['port']}"
            response = requests.get(f'{es_url}/', timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception:
            pass
        return None
    
    def get_index_info(self, source_config: Dict, index_name: str = None) -> Optional[Dict]:
        """
        获取索引信息
        
        Args:
            source_config: 源配置字典
            index_name: 索引名称，默认使用target_index
            
        Returns:
            索引信息字典，获取失败返回None
        """
        index_name = index_name or self.target_index
        try:
            es_url = f"http://{source_config['host']}:{source_config['port']}"
            response = requests.get(f'{es_url}/{index_name}', timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception:
            pass
        return None
    
    def create_time_range_query(self, start_time_ms: int, end_time_ms: Optional[int] = None, 
                               size: int = 1000, additional_filters: Optional[List[Dict]] = None) -> Dict:
        """
        创建时间范围查询
        
        Args:
            start_time_ms: 开始时间戳（毫秒）
            end_time_ms: 结束时间戳（毫秒），None表示到当前时间
            size: 返回结果数量限制
            additional_filters: 额外的过滤条件
            
        Returns:
            Elasticsearch查询字典
        """
        time_filter = {
            "range": {
                "time": {
                    "gte": start_time_ms
                }
            }
        }
        
        if end_time_ms:
            time_filter["range"]["time"]["lte"] = end_time_ms
        
        must_filters = [
            {"exists": {"field": "mess.messType"}},
            time_filter
        ]
        
        if additional_filters:
            must_filters.extend(additional_filters)
        
        return {
            "query": {
                "bool": {
                    "must": must_filters
                }
            },
            "sort": [{"time": {"order": "desc"}}],
            "size": size
        }
    
    def create_aggregation_query(self, field: str, size: int = 2000, 
                                start_time_ms: Optional[int] = None) -> Dict:
        """
        创建聚合查询
        
        Args:
            field: 聚合字段
            size: 聚合桶数量限制
            start_time_ms: 开始时间戳（毫秒），None表示不限制时间
            
        Returns:
            Elasticsearch聚合查询字典
        """
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": field}}
                    ]
                }
            },
            "aggs": {
                "field_values": {
                    "terms": {
                        "field": f"{field}.keyword",
                        "size": size
                    }
                }
            },
            "size": 0
        }
        
        if start_time_ms:
            query["query"]["bool"]["must"].append({
                "range": {
                    "time": {
                        "gte": start_time_ms
                    }
                }
            })
        
        return query


def create_client(es_sources: Optional[List[Dict]] = None, target_index: str = DEFAULT_TARGET_INDEX,
                 debug_mode: bool = False, file_output=None) -> ElasticsearchClient:
    """
    创建Elasticsearch客户端实例
    
    Args:
        es_sources: Elasticsearch源配置列表
        target_index: 目标索引名称
        debug_mode: 是否启用调试模式
        file_output: 文件输出管理器实例
        
    Returns:
        Elasticsearch客户端实例
    """
    return ElasticsearchClient(
        es_sources=es_sources,
        target_index=target_index,
        debug_mode=debug_mode,
        file_output=file_output
    )
