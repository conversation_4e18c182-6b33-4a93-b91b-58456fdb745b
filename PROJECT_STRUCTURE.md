# OCPP 1.6 Multi-Source Exporter - 项目架构文档

## 项目概述

OCPP 1.6 Multi-Source Exporter 是一个符合OCPP 1.6标准的充电桩数据导出器，支持多源Elasticsearch数据收集、事务配对分析、消息类型自动检测，并提供Prometheus指标导出功能。

## 项目结构

```
ocpp_exporter/
├── constants.py              # OCPP 1.6标准常量定义
├── file_output.py           # 文件输出和日志管理
├── time_utils.py            # 时间处理工具函数
├── document_parser.py       # OCPP文档解析和验证
├── elasticsearch_client.py  # 多源Elasticsearch搜索
├── transaction_analyzer.py  # 事务配对分析引擎
├── message_type_detector.py # 消息类型自动检测
├── metrics_manager.py       # Prometheus指标管理
├── exporter.py             # 主导出器类（集成所有模块）
├── main.py                 # 程序入口点
├── __init__.py             # Python包初始化
└── PROJECT_STRUCTURE.md    # 项目架构文档
```

## 模块详细说明

### 1. constants.py - OCPP 1.6标准常量定义

**功能**：
- 定义OCPP 1.6标准错误代码映射
- 定义充电桩状态枚举
- 定义OCPP消息类型列表
- 配置默认参数和限制

**主要常量**：
- `OCPP_ERROR_CODES`: 错误代码到数值的映射
- `CHARGE_POINT_STATUS`: 充电桩状态到数值的映射
- `OCPP_MESSAGE_TYPES`: 支持的OCPP消息类型列表
- `DEFAULT_ES_SOURCES`: 默认Elasticsearch源配置
- `SCHEDULER_INTERVALS`: 调度器时间间隔配置

**依赖关系**：无依赖，被所有其他模块引用

### 2. file_output.py - 文件输出和日志管理

**功能**：
- 提供统一的文件输出接口
- 支持同时输出到控制台和文件
- 提供格式化输出方法（表格、列表、JSON等）
- 自动创建带时间戳的文件名

**主要类**：
- `FileOutputManager`: 文件输出管理器

**依赖关系**：无依赖，被所有需要输出的模块使用

### 3. time_utils.py - 时间处理工具函数

**功能**：
- 毫秒时间戳与日期时间字符串转换
- 时间范围计算
- 时间窗口信息生成
- 相对时间描述

**主要函数**：
- `convert_ms_to_datetime()`: 毫秒时间戳转ISO 8601字符串
- `calculate_time_range_hours()`: 计算时间范围小时数
- `get_time_range_ms()`: 获取时间范围毫秒时间戳
- `is_time_near_boundary()`: 检查时间是否接近边界

**依赖关系**：无依赖，被需要时间处理的模块使用

### 4. document_parser.py - OCPP文档解析和验证

**功能**：
- 解析Elasticsearch中的OCPP文档
- 验证和标准化OCPP消息数据
- 提取事务相关数据
- 调试信息输出

**主要类**：
- `OCPPDocumentParser`: OCPP文档解析器

**依赖关系**：
- 依赖：`constants.py`
- 被依赖：`exporter.py`, `metrics_manager.py`

### 5. elasticsearch_client.py - 多源Elasticsearch搜索

**功能**：
- 管理多个Elasticsearch源连接
- 并发执行搜索查询
- 连接状态监控
- 查询构建工具

**主要类**：
- `ElasticsearchClient`: 多源Elasticsearch客户端

**依赖关系**：
- 依赖：`constants.py`
- 被依赖：`exporter.py`, `message_type_detector.py`

### 6. transaction_analyzer.py - 事务配对分析引擎

**功能**：
- 分析StartTransaction和StopTransaction配对
- 检测未配对的事务
- 按ChargePoint和连接器分组分析
- 时间边界检查

**主要类**：
- `TransactionAnalyzer`: 事务配对分析器

**依赖关系**：
- 依赖：`time_utils.py`
- 被依赖：`exporter.py`

### 7. message_type_detector.py - 消息类型自动检测

**功能**：
- 自动发现OCPP消息类型
- 识别未知消息类型
- 统计消息类型分布
- 按数据源分析消息类型

**主要类**：
- `MessageTypeDetector`: 消息类型检测器

**依赖关系**：
- 依赖：`constants.py`, `time_utils.py`
- 被依赖：`exporter.py`

### 8. metrics_manager.py - Prometheus指标管理

**功能**：
- 初始化所有Prometheus指标
- 更新OCPP核心指标
- 更新消息类型检测指标
- 更新事务配对分析指标
- 更新数据源状态指标

**主要类**：
- `MetricsManager`: Prometheus指标管理器

**依赖关系**：
- 依赖：`constants.py`, `time_utils.py`
- 被依赖：`exporter.py`

### 9. exporter.py - 主导出器类

**功能**：
- 集成所有功能模块
- 协调数据收集和处理流程
- 管理调度器
- 提供统一的API接口

**主要类**：
- `OCPP16ChargingStationExporter`: 主导出器类

**依赖关系**：
- 依赖：所有其他模块
- 被依赖：`main.py`

### 10. main.py - 程序入口点

**功能**：
- 程序主入口函数
- 配置管理
- 启动Prometheus HTTP服务器
- 提供多种运行模式

**主要函数**：
- `main()`: 标准启动模式
- `main_with_custom_config()`: 自定义配置启动
- `run_single_collection()`: 单次数据收集
- `run_analysis_only()`: 仅运行分析

**依赖关系**：
- 依赖：`constants.py`, `file_output.py`, `exporter.py`

### 11. __init__.py - Python包初始化

**功能**：
- 定义包的公共接口
- 导出主要组件
- 提供快速启动函数
- 包信息管理

**依赖关系**：导入所有模块的公共接口

## 数据流图

```
Elasticsearch Sources → elasticsearch_client.py
                              ↓
Raw Documents → document_parser.py → Parsed OCPP Messages
                              ↓
Parsed Messages → transaction_analyzer.py → Transaction Analysis
                              ↓
Parsed Messages → message_type_detector.py → Message Type Analysis
                              ↓
All Analysis Results → metrics_manager.py → Prometheus Metrics
                              ↓
All Components → exporter.py → Unified Interface
                              ↓
Configuration → main.py → Running Application
```

## 核心功能模块

### 数据收集流程
1. **连接测试**: `elasticsearch_client.py` 测试所有ES源连接
2. **数据查询**: 并发查询所有活跃源
3. **文档解析**: `document_parser.py` 解析和验证OCPP消息
4. **数据去重**: 按ChargePoint+Connector+MessageType去重
5. **指标更新**: `metrics_manager.py` 更新Prometheus指标

### 分析功能
1. **消息类型检测**: 自动发现和分类OCPP消息类型
2. **事务配对分析**: 分析StartTransaction和StopTransaction配对情况
3. **时间边界检查**: 检测接近查询时间窗口边界的事务

### 输出功能
1. **Prometheus指标**: 实时指标导出
2. **文件输出**: 详细日志和分析结果保存
3. **控制台输出**: 实时状态和调试信息

## 配置说明

### Elasticsearch源配置
```python
es_sources = [
    {
        'name': 'primary',
        'host': '************',
        'port': 9200,
        'priority': 1
    }
]
```

### 调度器配置
- 数据收集: 每2分钟
- 消息类型发现: 每60分钟
- 事务配对分析: 每30分钟

## 使用示例

### 基本使用
```python
from ocpp_exporter import main
main()
```

### 自定义配置
```python
from ocpp_exporter import main_with_custom_config

main_with_custom_config(
    es_sources=[...],
    debug_mode=True,
    prometheus_port=8000
)
```

### 单次分析
```python
from ocpp_exporter import run_analysis_only

results = run_analysis_only(
    time_range_hours=24
)
```

## 扩展指南

### 添加新的消息类型
1. 在 `constants.py` 中添加到 `OCPP_MESSAGE_TYPES`
2. 在 `document_parser.py` 中添加解析逻辑
3. 在 `metrics_manager.py` 中添加相关指标

### 添加新的数据源
1. 在 `elasticsearch_client.py` 中扩展客户端支持
2. 更新配置格式
3. 测试连接和查询功能

### 添加新的分析功能
1. 创建新的分析器模块
2. 在 `exporter.py` 中集成
3. 在 `metrics_manager.py` 中添加相关指标

## 性能优化

1. **并发查询**: 使用ThreadPoolExecutor并发查询多个ES源
2. **数据去重**: 高效的去重算法减少内存使用
3. **增量更新**: 仅更新变化的指标
4. **缓存机制**: 缓存最新状态减少重复计算

## 监控和调试

1. **详细日志**: 所有操作都有详细的日志记录
2. **文件输出**: 所有输出同时保存到文件
3. **调试模式**: 提供详细的调试信息
4. **指标监控**: 通过Prometheus指标监控系统状态
