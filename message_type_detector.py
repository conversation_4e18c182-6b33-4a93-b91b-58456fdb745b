#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Message Type Auto-Detection Module
消息类型自动检测模块，负责发现和分析OCPP消息类型
"""

import logging
from datetime import datetime
from typing import Dict, List, Set, Optional
from collections import Counter
from constants import OCPP_MESSAGE_TYPES, ELASTICSEARCH_AGGREGATION_SIZE_LIMIT
from time_utils import get_current_time_ms

logger = logging.getLogger(__name__)


class MessageTypeDetector:
    """消息类型检测器"""
    
    def __init__(self, debug_mode: bool = False, file_output=None):
        """
        初始化消息类型检测器
        
        Args:
            debug_mode: 是否启用调试模式
            file_output: 文件输出管理器实例
        """
        self.debug_mode = debug_mode
        self.file_output = file_output
        self.discovered_message_types = set()
        self.message_type_stats = Counter()
        self.message_type_by_source = {}
        self.last_message_type_scan = None
    
    def analyze_message_types(self, data: List[Dict]) -> Dict:
        """
        分析和检测数据中的所有消息类型
        
        Args:
            data: 解析后的OCPP消息数据列表
            
        Returns:
            消息类型分析结果字典
        """
        if not data:
            return {}
        
        if self.file_output:
            self.file_output.save_header("🔍 MESSAGETYPE DETECTION ANALYSIS")
        logger.info("🔍 Starting MessageType Detection Analysis...")
        
        # 初始化跟踪结构
        discovered_types = set()
        type_counts = Counter()
        source_type_mapping = {}
        unknown_types = set()
        predefined_types = set(OCPP_MESSAGE_TYPES)
        
        # 分析每条记录
        for record in data:
            message_type = record.get('message_type', '')
            data_source = record.get('data_source', 'unknown')
            
            if not message_type:
                continue
                
            # 添加到发现的类型
            discovered_types.add(message_type)
            type_counts[message_type] += 1
            
            # 按源跟踪
            if data_source not in source_type_mapping:
                source_type_mapping[data_source] = set()
            source_type_mapping[data_source].add(message_type)
            
            # 检查是否为未知类型
            if message_type not in predefined_types:
                unknown_types.add(message_type)
                if self.debug_mode and self.file_output:
                    unknown_msg = f"🔍 UNKNOWN MessageType discovered: {message_type} from {data_source}"
                    self.file_output.save_and_print(unknown_msg)
        
        # 更新类级别跟踪
        self.discovered_message_types.update(discovered_types)
        self.message_type_stats.update(type_counts)
        self.message_type_by_source.update(source_type_mapping)
        self.last_message_type_scan = datetime.now()
        
        # 创建分析结果
        analysis_result = {
            'total_discovered': len(discovered_types),
            'discovered_types': sorted(discovered_types),
            'unknown_types': sorted(unknown_types),
            'predefined_types': sorted(predefined_types),
            'missing_predefined': sorted(predefined_types - discovered_types),
            'type_counts': dict(type_counts),
            'source_type_mapping': {k: sorted(v) for k, v in source_type_mapping.items()},
            'scan_time': self.last_message_type_scan.isoformat()
        }
        
        # 打印详细分析
        if self.debug_mode and self.file_output:
            self._print_detailed_analysis(analysis_result, discovered_types, predefined_types, type_counts, source_type_mapping)
        
        summary_msg = (f"🔍 MessageType Analysis: {len(discovered_types)} total types, "
                      f"{len(unknown_types)} unknown types, {sum(type_counts.values())} total messages")
        logger.info(summary_msg)
        if self.file_output:
            self.file_output.save_and_print(summary_msg)
        
        return analysis_result
    
    def _print_detailed_analysis(self, analysis_result: Dict, discovered_types: Set, 
                                predefined_types: Set, type_counts: Counter, source_type_mapping: Dict):
        """打印详细的分析结果"""
        if not self.file_output:
            return
            
        unknown_types = set(analysis_result['unknown_types'])
        
        self.file_output.save_and_print(f"📊 Total Discovered Types: {len(discovered_types)}")
        self.file_output.save_and_print(f"📋 Discovered Types: {', '.join(sorted(discovered_types))}")
        self.file_output.save_and_print(f"❓ Unknown Types: {', '.join(sorted(unknown_types)) if unknown_types else 'None'}")
        self.file_output.save_and_print(f"✅ Predefined Types Found: {len(discovered_types & predefined_types)}")
        self.file_output.save_and_print(f"❌ Missing Predefined Types: {', '.join(sorted(predefined_types - discovered_types))}")
        self.file_output.save_and_print(f"\n📈 Message Type Counts:")
        for msg_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            is_predefined = "✅" if msg_type in predefined_types else "❓"
            self.file_output.save_and_print(f"  {is_predefined} {msg_type}: {count}")
        self.file_output.save_and_print(f"\n🌐 Source Distribution:")
        for source, types in source_type_mapping.items():
            self.file_output.save_and_print(f"  {source}: {', '.join(sorted(types))}")
    
    def discover_all_message_types(self, elasticsearch_client, time_range_hours: Optional[int] = None) -> Dict:
        """
        通过Elasticsearch聚合进行全面的消息类型发现
        
        Args:
            elasticsearch_client: Elasticsearch客户端实例
            time_range_hours: 时间范围小时数
            
        Returns:
            消息类型发现结果字典
        """
        if time_range_hours is None:
            from time_utils import calculate_time_range_hours
            time_range_hours = calculate_time_range_hours()
            
        if self.file_output:
            self.file_output.save_header(f"🔍 COMPREHENSIVE MESSAGETYPE DISCOVERY ({time_range_hours} hours)")
        logger.info(f"🔍 Starting comprehensive MessageType discovery scan ({time_range_hours} hours)...")
        
        # 计算时间范围
        current_time_ms = get_current_time_ms()
        start_time_ms = current_time_ms - (time_range_hours * 60 * 60 * 1000)
        
        # 用于获取所有消息类型的查询
        discovery_query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "mess.messType"}},
                        {
                            "range": {
                                "time": {
                                    "gte": start_time_ms
                                }
                            }
                        }
                    ]
                }
            },
            "aggs": {
                "message_types": {
                    "terms": {
                        "field": "mess.messType.keyword",
                        "size": ELASTICSEARCH_AGGREGATION_SIZE_LIMIT
                    }
                },
                "doc_message_types": {
                    "terms": {
                        "field": "messType.keyword", 
                        "size": ELASTICSEARCH_AGGREGATION_SIZE_LIMIT
                    }
                }
            },
            "sort": [{"time": {"order": "desc"}}],
            "size": 2000
        }
        
        logger.info("🔍 Executing comprehensive MessageType discovery query...")
        if self.file_output:
            self.file_output.save_and_print("🔍 Executing comprehensive MessageType discovery query...")
        
        search_results = elasticsearch_client.search_all_elasticsearch_sources(discovery_query)
        
        all_discovered_data = []
        aggregation_results = {}
        
        # 处理每个源的结果
        for response, source_name in search_results:
            if not response or 'hits' not in response:
                continue
            
            hits = response['hits']['hits']
            discovery_msg = f"🔍 Source {source_name}: Found {len(hits)} records for MessageType discovery"
            logger.info(discovery_msg)
            if self.file_output:
                self.file_output.save_and_print(discovery_msg)
            
            # 提取聚合结果
            aggs = response.get('aggregations', {})
            if aggs:
                aggregation_results[source_name] = {
                    'mess_types': [(bucket['key'], bucket['doc_count']) for bucket in aggs.get('message_types', {}).get('buckets', [])],
                    'doc_types': [(bucket['key'], bucket['doc_count']) for bucket in aggs.get('doc_message_types', {}).get('buckets', [])]
                }
            
            # 解析文档
            from document_parser import create_parser
            parser = create_parser(debug_mode=self.debug_mode, file_output=self.file_output)
            
            for hit in hits:
                source_doc = hit['_source']
                parsed = parser.parse_document(source_doc, source_name)
                if parsed and parsed.get('message_type'):
                    all_discovered_data.append(parsed)
        
        # 分析发现的数据
        analysis_result = self.analyze_message_types(all_discovered_data)
        analysis_result['aggregations'] = aggregation_results
        
        # 打印聚合结果
        if self.debug_mode and self.file_output:
            self._print_aggregation_results(aggregation_results)
        
        return analysis_result
    
    def _print_aggregation_results(self, aggregation_results: Dict):
        """打印聚合结果"""
        if not self.file_output:
            return
            
        self.file_output.save_and_print(f"\n🔍 ELASTICSEARCH AGGREGATION RESULTS:")
        for source, agg_data in aggregation_results.items():
            self.file_output.save_and_print(f"\n📊 Source: {source}")
            self.file_output.save_and_print(f"  mess.messType field:")
            for msg_type, count in agg_data['mess_types']:
                self.file_output.save_and_print(f"    {msg_type}: {count}")
            self.file_output.save_and_print(f"  messType field:")
            for msg_type, count in agg_data['doc_types']:
                self.file_output.save_and_print(f"    {msg_type}: {count}")
    
    def get_discovered_types(self) -> Set[str]:
        """获取已发现的消息类型集合"""
        return self.discovered_message_types.copy()
    
    def get_unknown_types(self) -> Set[str]:
        """获取未知消息类型集合"""
        predefined_types = set(OCPP_MESSAGE_TYPES)
        return self.discovered_message_types - predefined_types
    
    def get_type_statistics(self) -> Dict:
        """获取消息类型统计信息"""
        return dict(self.message_type_stats)
    
    def get_source_type_mapping(self) -> Dict:
        """获取源到消息类型的映射"""
        return {k: sorted(v) for k, v in self.message_type_by_source.items()}
    
    def get_last_scan_time(self) -> Optional[datetime]:
        """获取最后一次扫描时间"""
        return self.last_message_type_scan
    
    def is_known_message_type(self, message_type: str) -> bool:
        """检查消息类型是否为已知类型"""
        return message_type in OCPP_MESSAGE_TYPES
    
    def get_message_type_coverage(self) -> Dict:
        """获取消息类型覆盖率信息"""
        predefined_types = set(OCPP_MESSAGE_TYPES)
        discovered_predefined = self.discovered_message_types & predefined_types
        
        return {
            'total_predefined': len(predefined_types),
            'discovered_predefined': len(discovered_predefined),
            'coverage_percentage': (len(discovered_predefined) / len(predefined_types)) * 100 if predefined_types else 0,
            'missing_types': sorted(predefined_types - self.discovered_message_types),
            'unknown_types': sorted(self.discovered_message_types - predefined_types)
        }


def create_detector(debug_mode: bool = False, file_output=None) -> MessageTypeDetector:
    """
    创建消息类型检测器实例
    
    Args:
        debug_mode: 是否启用调试模式
        file_output: 文件输出管理器实例
        
    Returns:
        消息类型检测器实例
    """
    return MessageTypeDetector(debug_mode=debug_mode, file_output=file_output)
