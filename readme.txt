OCPP 1.6充电站导出器
├── 初始化模块 (__init__)
│   ├── 配置Elasticsearch多数据源
│   ├── 初始化消息类型检测模块
│   ├── 测试ES连接 (_test_elasticsearch_connections)
│   └── 初始化Prometheus指标 (_init_metrics)
│
├── 数据收集流程
│   ├── 获取OCPP数据 (_fetch_ocpp_data)
│   │   ├── 构建时间范围查询
│   │   ├── 多源并发搜索 (_search_all_elasticsearch_sources)
│   │   ├── 备用搜索策略（无时间限制）
│   │   └── 记录去重 (_deduplicate_records)
│   │
│   ├── 文档解析 (_parse_document)
│   │   ├── 提取mess对象数据
│   │   ├── 验证消息类型
│   │   ├── 规范化充电站ID (_normalize_chargepoint_id)
│   │   └── 验证连接器ID (_validate_connector_id)
│   │
│   └── 单源搜索 (_search_elasticsearch_source)
│
├── 消息类型检测模块 (NEW)
│   ├── 消息类型分析 (_analyze_message_types)
│   │   ├── 收集所有消息类型
│   │   ├── 统计类型出现频率
│   │   ├── 识别未知消息类型
│   │   └── 按数据源分类
│   │
│   ├── 全面消息发现 (_discover_all_message_types)
│   │   ├── 执行聚合查询
│   │   └── 分析所有消息类型
│   │
│   └── 更新消息类型指标 (_update_message_type_metrics)
│
├── 指标更新流程 (_update_metrics)
│   ├── 调用消息类型分析
│   ├── 处理状态通知消息
│   ├── 处理心跳消息
│   ├── 处理交易消息
│   ├── 更新状态指标
│   ├── 更新错误代码指标
│   ├── 更新故障状态
│   └── 更新最后更新时间
│
├── 调度系统
│   ├── 启动调度器 (start_scheduler)
│   ├── 定期数据收集 (collect_and_update)
│   └── 定期消息类型发现 (run_message_type_discovery)
│
└── 主程序 (main)
    ├── 配置数据源
    ├── 创建导出器实例
    ├── 启动调度器
    ├── 启动Prometheus HTTP服务器
    └── 保持运行
