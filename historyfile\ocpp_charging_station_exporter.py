#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCPP 1.6 Compliant Charging Station Exporter - Multi-Source Enhanced
Enhanced for multiple Elasticsearch data sources and improved data aggregation
WITH MESSAGETYPE DETECTION MODULE, TRANSACTION PAIRING ANALYSIS, AND FILE OUTPUT
"""

import json
import time
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Tuple, Set
from collections import Counter, defaultdict
import requests
from prometheus_client import CollectorRegistry, Gauge, Counter as PrometheusCounter, Info, start_http_server
from prometheus_client.core import REGISTRY
import threading
import schedule
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FileOutputManager:
    """文件输出管理器，负责将所有输出同时保存到文件"""
    
    def __init__(self, output_file: str = "ocpp_exporter_output.txt"):
        self.output_file = output_file
        self.ensure_output_directory()
    
    def ensure_output_directory(self):
        """确保输出目录存在"""
        directory = os.path.dirname(self.output_file)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
    
    def save_output(self, content: str, add_timestamp: bool = True):
        """保存内容到文件"""
        try:
            timestamp = f"[{datetime.now().isoformat()}] " if add_timestamp else ""
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp}{content}\n")
                f.flush()
        except Exception as e:
            print(f"Failed to save output to file: {e}")
    
    def save_and_print(self, content: str, add_timestamp: bool = True):
        """同时打印到控制台和保存到文件"""
        print(content)
        self.save_output(content, add_timestamp)
    
    def save_separator(self, char: str = "=", length: int = 100):
        """保存分隔符"""
        separator = char * length
        self.save_and_print(separator)
    
    def save_header(self, title: str, char: str = "=", length: int = 100):
        """保存标题头"""
        self.save_separator(char, length)
        self.save_and_print(title)
        self.save_separator(char, length)

class OCCP16ChargingStationExporter:
    """OCPP 1.6 Compliant Charging Station Exporter - Multi-Source Enhanced with File Output"""

    # OCPP 1.6 Standard Error Codes
    OCPP_ERROR_CODES = {
        'NoError': 0,
        'ConnectorLockFailure': 1,
        'EVCommunicationError': 2,
        'GroundFailure': 3,
        'HighTemperature': 4,
        'InternalError': 5,
        'LocalListConflict': 6,
        'OtherError': 7,
        'OverCurrentFailure': 8,
        'OverVoltage': 9,
        'PowerMeterFailure': 10,
        'PowerSwitchFailure': 11,
        'ReaderFailure': 12,
        'ResetFailure': 13,
        'UnderVoltage': 14,
        'WeakSignal': 15
    }

    # OCPP 1.6 ChargePointStatus
    CHARGE_POINT_STATUS = {
        'Available': 0,
        'Preparing': 1,
        'Charging': 2,
        'SuspendedEVSE': 3,
        'SuspendedEV': 4,
        'Finishing': 5,
        'Reserved': 6,
        'Unavailable': 7,
        'Faulted': 8
    }

    # OCPP 1.6 Message Types (expanded based on actual data)
    OCPP_MESSAGE_TYPES = [
        'StatusNotification',
        'Heartbeat',
        'BootNotification', 
        'StartTransaction',
        'StopTransaction',
        'MeterValues',
        'Authorize',
        'DataTransfer',
        'DiagnosticsStatusNotification',
        'FirmwareStatusNotification',
        'RemoteStartTransaction',
        'RemoteStopTransaction',
        'Reset',
        'ChangeConfiguration',
        'GetConfiguration',
        'ClearCache',
        'UnlockConnector'
    ]

    def __init__(self, es_sources=None, debug_mode=True, output_file="ocpp_exporter_output.txt"):
        """
        Initialize OCPP exporter with multiple Elasticsearch sources and file output
        
        Args:
            es_sources: List of ES source configs or None for default
            debug_mode: Enable detailed debugging output
            output_file: Path to output file for saving all output
        """
        # Initialize file output manager first
        self.file_output = FileOutputManager(output_file)
        self.file_output.save_header(f"OCPP 1.6 Multi-Source Exporter Started - {datetime.now().isoformat()}")
        
        if es_sources is None:
            # Default configuration with both data sources
            es_sources = [
                {
                    'name': 'primary',
                    'host': '************',
                    'port': 9200,
                    'priority': 1
                },
                {
                    'name': 'secondary', 
                    'host': '*************',
                    'port': 9200,
                    'priority': 2
                }
            ]
        
        self.es_sources = es_sources
        self.target_index = 'international_protocol'
        self.debug_mode = debug_mode
        
        # MessageType Detection Module
        self.discovered_message_types = set()
        self.message_type_stats = Counter()
        self.message_type_by_source = {}
        self.last_message_type_scan = None
        
        # Transaction Pairing Analysis Module
        self.transaction_pairing_stats = {}
        self.unpaired_transactions = {}
        self.last_pairing_analysis = None
        
        # Test all ES connections
        self.active_sources = []
        self._test_elasticsearch_connections()
        
        if not self.active_sources:
            error_msg = "No active Elasticsearch sources available"
            self.file_output.save_and_print(f"ERROR: {error_msg}")
            raise Exception(error_msg)
        
        # Create custom registry
        self.registry = CollectorRegistry()
        
        # Initialize Prometheus metrics
        self._init_metrics()
        
        # Cache latest status
        self.station_status_cache = {}
        self.last_update_time = datetime.now()
        
    def _calculate_time_range_hours(self) -> int:
        """
        计算从2024年1月1日到2025年8月31日的小时数
        Returns:
            小时数
        """
        start = datetime(2024, 1, 1)
        end = datetime(2025, 8, 31, 23, 59, 59)
        hours = int((end - start).total_seconds() / 3600)
        self.file_output.save_and_print(f"Calculated time range: {start.isoformat()} to {end.isoformat()} = {hours} hours")
        return hours
        
    def _test_elasticsearch_connections(self):
        """Test connections to all configured Elasticsearch sources"""
        self.file_output.save_header("Testing Elasticsearch Connections")
        
        for source_config in self.es_sources:
            try:
                es_url = f"http://{source_config['host']}:{source_config['port']}"
                response = requests.get(f'{es_url}/', timeout=10)
                
                if response.status_code == 200:
                    info = response.json()
                    source_config['url'] = es_url
                    source_config['cluster_name'] = info['cluster_name']
                    source_config['version'] = info['version']['number']
                    
                    self.active_sources.append(source_config)
                    
                    success_msg = (f"✅ Connected to ES source '{source_config['name']}': "
                                  f"{source_config['host']}:{source_config['port']} "
                                  f"(cluster: {info['cluster_name']}, version: {info['version']['number']})")
                    logger.info(success_msg)
                    self.file_output.save_and_print(success_msg)
                else:
                    warning_msg = f"⚠️ ES source '{source_config['name']}' returned HTTP {response.status_code}"
                    logger.warning(warning_msg)
                    self.file_output.save_and_print(warning_msg)
                    
            except Exception as e:
                error_msg = (f"❌ Failed to connect to ES source '{source_config['name']}' "
                           f"({source_config['host']}:{source_config['port']}): {e}")
                logger.error(error_msg)
                self.file_output.save_and_print(error_msg)
        
        summary_msg = f"Active Elasticsearch sources: {len(self.active_sources)}/{len(self.es_sources)}"
        logger.info(summary_msg)
        self.file_output.save_and_print(summary_msg)
        
    def _init_metrics(self):
        """Initialize OCPP 1.6 compliant Prometheus metrics"""
        self.file_output.save_and_print("Initializing Prometheus metrics...")
        
        # Core status metric
        self.station_status = Gauge(
            'ocpp_chargepoint_status',
            'OCPP 1.6 ChargePoint status (0=Available,1=Preparing,2=Charging,3=SuspendedEVSE,4=SuspendedEV,5=Finishing,6=Reserved,7=Unavailable,8=Faulted)',
            ['chargepoint_id', 'connector_id', 'vendor_id', 'data_source'],
            registry=self.registry
        )
        
        # Enhanced fault status with OCPP error codes
        self.station_fault_status = Gauge(
            'ocpp_chargepoint_fault',
            'OCPP 1.6 ChargePoint fault status (0=normal, 1=fault)',
            ['chargepoint_id', 'connector_id', 'error_code', 'vendor_error_code', 'data_source'],
            registry=self.registry
        )
        
        # Error code metric
        self.error_code_value = Gauge(
            'ocpp_error_code',
            'OCPP 1.6 Error code numeric value',
            ['chargepoint_id', 'connector_id', 'error_code', 'data_source'],
            registry=self.registry
        )
        
        # Transaction metrics
        self.active_transactions = Gauge(
            'ocpp_active_transactions',
            'Number of active charging transactions',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # OCPP message counters
        self.ocpp_messages_total = PrometheusCounter(
            'ocpp_messages_total',
            'Total OCPP messages processed',
            ['chargepoint_id', 'message_type', 'direction', 'data_source'],
            registry=self.registry
        )
        
        # Fault counters
        self.fault_total = PrometheusCounter(
            'ocpp_faults_total',
            'Total OCPP faults by error code',
            ['chargepoint_id', 'connector_id', 'error_code', 'data_source'],
            registry=self.registry
        )
        
        # Heartbeat tracking
        self.last_heartbeat = Gauge(
            'ocpp_last_heartbeat_timestamp',
            'Last heartbeat timestamp (Unix time)',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # Data timestamp
        self.last_update = Gauge(
            'ocpp_last_update_timestamp',
            'Last data update timestamp (Unix time)',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # ChargePoint information
        self.chargepoint_info = Info(
            'ocpp_chargepoint_info',
            'OCPP 1.6 ChargePoint information',
            ['chargepoint_id', 'data_source'],
            registry=self.registry
        )
        
        # Data discovery metrics
        self.data_discovery = Info(
            'ocpp_data_discovery',
            'OCPP data discovery information from all sources',
            registry=self.registry
        )
        
        # Multi-source metrics
        self.source_status = Gauge(
            'ocpp_data_source_status',
            'Data source availability status (1=active, 0=inactive)',
            ['source_name', 'source_host', 'source_port'],
            registry=self.registry
        )
        
        self.source_records_count = Gauge(
            'ocpp_data_source_records',
            'Number of records retrieved from each data source',
            ['source_name', 'source_host'],
            registry=self.registry
        )
        
        # MessageType Detection Metrics
        self.message_type_discovered = PrometheusCounter(
            'ocpp_message_types_discovered_total',
            'Total number of distinct message types discovered',
            ['data_source'],
            registry=self.registry
        )
        
        self.message_type_count = Gauge(
            'ocpp_message_type_count',
            'Count of messages by message type discovered from data',
            ['message_type', 'data_source', 'predefined'],
            registry=self.registry
        )
        
        self.message_type_discovery = Info(
            'ocpp_message_type_discovery',
            'OCPP message type discovery information',
            registry=self.registry
        )
        
        self.unknown_message_types = PrometheusCounter(
            'ocpp_unknown_message_types_total',
            'Count of unknown/undocumented message types discovered',
            ['message_type', 'data_source'],
            registry=self.registry
        )

        # Transaction Pairing Analysis Metrics
        self.unpaired_start_transactions = Gauge(
            'ocpp_unpaired_start_transactions',
            'Number of StartTransaction messages without matching StopTransaction',
            ['chargepoint_id', 'connector_id', 'data_source'],
            registry=self.registry
        )
        
        self.unpaired_stop_transactions = Gauge(
            'ocpp_unpaired_stop_transactions', 
            'Number of StopTransaction messages without matching StartTransaction',
            ['chargepoint_id', 'connector_id', 'data_source'],
            registry=self.registry
        )
        
        self.transaction_pairing_info = Info(
            'ocpp_transaction_pairing_analysis',
            'OCPP transaction pairing analysis information',
            registry=self.registry
        )
        
        self.query_time_window = Info(
            'ocpp_query_time_window',
            'Current query time window information',
            registry=self.registry
        )
        
        self.file_output.save_and_print("✅ Prometheus metrics initialized successfully")

    def _analyze_transaction_pairing(self, data: List[Dict], start_time_ms: int, end_time_ms: int) -> Dict:
        """Analyze StartTransaction and StopTransaction pairing"""
        if not data:
            return {}
        
        self.file_output.save_header("🔄 TRANSACTION PAIRING ANALYSIS", "=", 100)
        logger.info("🔄 Starting Transaction Pairing Analysis...")
        
        # Separate transactions by type
        start_transactions = []
        stop_transactions = []
        
        for record in data:
            message_type = record.get('message_type', '')
            if message_type == 'StartTransaction':
                start_transactions.append(record)
            elif message_type == 'StopTransaction':
                stop_transactions.append(record)
        
        info_msg = f"📊 Found {len(start_transactions)} StartTransaction and {len(stop_transactions)} StopTransaction messages"
        logger.info(info_msg)
        self.file_output.save_and_print(info_msg)
        
        # Group by ChargePoint + Connector
        start_by_cp_conn = defaultdict(list)
        stop_by_cp_conn = defaultdict(list)
        
        for record in start_transactions:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}"
            start_by_cp_conn[key].append(record)
            
        for record in stop_transactions:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}"
            stop_by_cp_conn[key].append(record)
        
        # Find unpaired transactions
        unpaired_starts = {}
        unpaired_stops = {}
        all_chargepoints = set(list(start_by_cp_conn.keys()) + list(stop_by_cp_conn.keys()))
        
        for cp_conn_key in all_chargepoints:
            starts = start_by_cp_conn.get(cp_conn_key, [])
            stops = stop_by_cp_conn.get(cp_conn_key, [])
            
            start_count = len(starts)
            stop_count = len(stops)
            
            if start_count != stop_count:
                chargepoint_id, connector_id = cp_conn_key.rsplit('_', 1)
                
                if start_count > stop_count:
                    unpaired_starts[cp_conn_key] = {
                        'chargepoint_id': chargepoint_id,
                        'connector_id': connector_id,
                        'start_count': start_count,
                        'stop_count': stop_count,
                        'unpaired_count': start_count - stop_count,
                        'start_records': starts,
                        'stop_records': stops,
                        'data_sources': list(set([r.get('data_source', 'unknown') for r in starts + stops]))
                    }
                    
                if stop_count > start_count:
                    unpaired_stops[cp_conn_key] = {
                        'chargepoint_id': chargepoint_id,
                        'connector_id': connector_id,
                        'start_count': start_count,
                        'stop_count': stop_count,
                        'unpaired_count': stop_count - start_count,
                        'start_records': starts,
                        'stop_records': stops,
                        'data_sources': list(set([r.get('data_source', 'unknown') for r in starts + stops]))
                    }
        
        # Create analysis results
        analysis_result = {
            'total_start_transactions': len(start_transactions),
            'total_stop_transactions': len(stop_transactions),
            'unpaired_starts': unpaired_starts,
            'unpaired_stops': unpaired_stops,
            'chargepoints_with_unpaired': len(set(list(unpaired_starts.keys()) + list(unpaired_stops.keys()))),
            'query_start_time_ms': start_time_ms,
            'query_end_time_ms': end_time_ms,
            'query_start_datetime': self._convert_ms_to_datetime(start_time_ms),
            'query_end_datetime': self._convert_ms_to_datetime(end_time_ms),
            'analysis_time': datetime.now().isoformat()
        }
        
        # Print detailed analysis
        if self.debug_mode:
            self.file_output.save_and_print(f"📅 Query Time Window:")
            self.file_output.save_and_print(f"   Start: {analysis_result['query_start_datetime']} ({start_time_ms})")
            self.file_output.save_and_print(f"   End:   {analysis_result['query_end_datetime']} ({end_time_ms})")
            self.file_output.save_and_print(f"📊 Transaction Counts:")
            self.file_output.save_and_print(f"   StartTransaction: {len(start_transactions)}")
            self.file_output.save_and_print(f"   StopTransaction: {len(stop_transactions)}")
            self.file_output.save_and_print(f"   Difference: {len(stop_transactions) - len(start_transactions)}")
            
            if unpaired_starts:
                self.file_output.save_and_print(f"\n⚠️  UNPAIRED START TRANSACTIONS ({len(unpaired_starts)} ChargePoint-Connector pairs):")
                for cp_conn, info in unpaired_starts.items():
                    self.file_output.save_and_print(f"   🔌 {info['chargepoint_id']} Connector {info['connector_id']}:")
                    self.file_output.save_and_print(f"      Start: {info['start_count']}, Stop: {info['stop_count']}, Unpaired: {info['unpaired_count']}")
                    self.file_output.save_and_print(f"      Sources: {', '.join(info['data_sources'])}")
                    
                    # Show transaction times to check if they're near time window boundaries
                    self.file_output.save_and_print(f"      StartTransaction times:")
                    for start_rec in info['start_records'][-3:]:  # Show last 3
                        tx_time = start_rec.get('doc_time', 0)
                        tx_datetime = self._convert_ms_to_datetime(tx_time)
                        near_start = abs(tx_time - start_time_ms) < (60 * 60 * 1000)  # Within 1 hour of window start
                        near_end = abs(tx_time - end_time_ms) < (60 * 60 * 1000)    # Within 1 hour of window end
                        boundary_info = "🚨 NEAR WINDOW BOUNDARY" if (near_start or near_end) else ""
                        self.file_output.save_and_print(f"        📅 {tx_datetime} ({tx_time}) {boundary_info}")
            
            if unpaired_stops:
                self.file_output.save_and_print(f"\n⚠️  UNPAIRED STOP TRANSACTIONS ({len(unpaired_stops)} ChargePoint-Connector pairs):")
                for cp_conn, info in unpaired_stops.items():
                    self.file_output.save_and_print(f"   🔌 {info['chargepoint_id']} Connector {info['connector_id']}:")
                    self.file_output.save_and_print(f"      Start: {info['start_count']}, Stop: {info['stop_count']}, Unpaired: {info['unpaired_count']}")
                    self.file_output.save_and_print(f"      Sources: {', '.join(info['data_sources'])}")
                    
                    # Show transaction times to check if they're near time window boundaries
                    self.file_output.save_and_print(f"      StopTransaction times:")
                    for stop_rec in info['stop_records'][-3:]:  # Show last 3
                        tx_time = stop_rec.get('doc_time', 0)
                        tx_datetime = self._convert_ms_to_datetime(tx_time)
                        near_start = abs(tx_time - start_time_ms) < (60 * 60 * 1000)  # Within 1 hour of window start
                        near_end = abs(tx_time - end_time_ms) < (60 * 60 * 1000)    # Within 1 hour of window end
                        boundary_info = "🚨 NEAR WINDOW BOUNDARY" if (near_start or near_end) else ""
                        self.file_output.save_and_print(f"        📅 {tx_datetime} ({tx_time}) {boundary_info}")
            
            if not unpaired_starts and not unpaired_stops:
                self.file_output.save_and_print(f"\n✅ ALL TRANSACTIONS ARE PROPERLY PAIRED!")
        
        # Update class-level tracking
        self.unpaired_transactions = {
            'unpaired_starts': unpaired_starts,
            'unpaired_stops': unpaired_stops
        }
        self.last_pairing_analysis = datetime.now()
        
        summary_msg = (f"🔄 Transaction Pairing Analysis: {len(unpaired_starts)} unpaired starts, "
                      f"{len(unpaired_stops)} unpaired stops, "
                      f"{analysis_result['chargepoints_with_unpaired']} affected ChargePoints")
        logger.info(summary_msg)
        self.file_output.save_and_print(summary_msg)
        
        return analysis_result
    
    def _update_transaction_pairing_metrics(self, analysis_result: Dict):
        """Update Prometheus metrics with transaction pairing analysis"""
        if not analysis_result:
            return
            
        try:
            logger.info("🔄 Updating Transaction Pairing metrics...")
            self.file_output.save_and_print("🔄 Updating Transaction Pairing metrics...")
            
            # Update time window info
            self.query_time_window.info({
                'start_time_ms': str(analysis_result['query_start_time_ms']),
                'end_time_ms': str(analysis_result['query_end_time_ms']),
                'start_datetime': analysis_result['query_start_datetime'],
                'end_datetime': analysis_result['query_end_datetime'],
                'window_duration_hours': str((analysis_result['query_end_time_ms'] - analysis_result['query_start_time_ms']) / (1000 * 60 * 60))
            })
            
            # Update transaction pairing analysis info
            self.transaction_pairing_info.info({
                'total_start_transactions': str(analysis_result['total_start_transactions']),
                'total_stop_transactions': str(analysis_result['total_stop_transactions']),
                'transaction_difference': str(analysis_result['total_stop_transactions'] - analysis_result['total_start_transactions']),
                'chargepoints_with_unpaired_starts': str(len(analysis_result['unpaired_starts'])),
                'chargepoints_with_unpaired_stops': str(len(analysis_result['unpaired_stops'])),
                'total_chargepoints_with_unpaired': str(analysis_result['chargepoints_with_unpaired']),
                'analysis_time': analysis_result['analysis_time']
            })
            
            # Update unpaired start transaction metrics
            for cp_conn, info in analysis_result['unpaired_starts'].items():
                for data_source in info['data_sources']:
                    self.unpaired_start_transactions.labels(
                        chargepoint_id=info['chargepoint_id'],
                        connector_id=info['connector_id'],
                        data_source=data_source
                    ).set(info['unpaired_count'])
            
            # Update unpaired stop transaction metrics
            for cp_conn, info in analysis_result['unpaired_stops'].items():
                for data_source in info['data_sources']:
                    self.unpaired_stop_transactions.labels(
                        chargepoint_id=info['chargepoint_id'],
                        connector_id=info['connector_id'],
                        data_source=data_source
                    ).set(info['unpaired_count'])
            
            success_msg = f"✅ Transaction Pairing metrics updated: {analysis_result['chargepoints_with_unpaired']} ChargePoints with unpaired transactions"
            logger.info(success_msg)
            self.file_output.save_and_print(success_msg)
            
        except Exception as e:
            error_msg = f"❌ Failed to update Transaction Pairing metrics: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)
            if self.debug_mode:
                import traceback
                traceback_msg = "DEBUG: Transaction Pairing metrics error traceback:"
                self.file_output.save_and_print(traceback_msg)
                self.file_output.save_and_print(traceback.format_exc())

    def _print_query_time_window(self, start_time_ms: int, end_time_ms: int, time_range_hours: int):
        """Print detailed query time window information"""
        start_datetime = self._convert_ms_to_datetime(start_time_ms)
        end_datetime = self._convert_ms_to_datetime(end_time_ms)
        
        self.file_output.save_header("⏰ QUERY TIME WINDOW INFORMATION")
        self.file_output.save_and_print(f"📅 Start Time: {start_datetime}")
        self.file_output.save_and_print(f"   Timestamp: {start_time_ms} ms")
        self.file_output.save_and_print(f"📅 End Time:   {end_datetime}")
        self.file_output.save_and_print(f"   Timestamp: {end_time_ms} ms")
        self.file_output.save_and_print(f"⏳ Duration:   {time_range_hours} hours")
        self.file_output.save_and_print(f"⏳ Duration:   {(end_time_ms - start_time_ms) / (1000 * 60 * 60):.2f} hours (calculated)")
        self.file_output.save_and_print(f"🕐 Current Time: {datetime.now().isoformat()}")
        
        window_msg = f"⏰ Query Time Window: {start_datetime} to {end_datetime} ({time_range_hours} hours)"
        logger.info(window_msg)
        self.file_output.save_and_print(window_msg)

    def _analyze_message_types(self, data: List[Dict]) -> Dict:
        """Analyze and detect all message types from collected data"""
        if not data:
            return {}
        
        self.file_output.save_header("🔍 MESSAGETYPE DETECTION ANALYSIS")
        logger.info("🔍 Starting MessageType Detection Analysis...")
        
        # Initialize tracking structures
        discovered_types = set()
        type_counts = Counter()
        source_type_mapping = {}
        unknown_types = set()
        predefined_types = set(self.OCPP_MESSAGE_TYPES)
        
        # Analyze each record
        for record in data:
            message_type = record.get('message_type', '')
            data_source = record.get('data_source', 'unknown')
            
            if not message_type:
                continue
                
            # Add to discovered types
            discovered_types.add(message_type)
            type_counts[message_type] += 1
            
            # Track by source
            if data_source not in source_type_mapping:
                source_type_mapping[data_source] = set()
            source_type_mapping[data_source].add(message_type)
            
            # Check if unknown type
            if message_type not in predefined_types:
                unknown_types.add(message_type)
                if self.debug_mode:
                    unknown_msg = f"🔍 UNKNOWN MessageType discovered: {message_type} from {data_source}"
                    self.file_output.save_and_print(unknown_msg)
        
        # Update class-level tracking
        self.discovered_message_types.update(discovered_types)
        self.message_type_stats.update(type_counts)
        self.message_type_by_source.update(source_type_mapping)
        self.last_message_type_scan = datetime.now()
        
        # Create analysis results
        analysis_result = {
            'total_discovered': len(discovered_types),
            'discovered_types': sorted(discovered_types),
            'unknown_types': sorted(unknown_types),
            'predefined_types': sorted(predefined_types),
            'missing_predefined': sorted(predefined_types - discovered_types),
            'type_counts': dict(type_counts),
            'source_type_mapping': {k: sorted(v) for k, v in source_type_mapping.items()},
            'scan_time': self.last_message_type_scan.isoformat()
        }
        
        # Print detailed analysis
        if self.debug_mode:
            self.file_output.save_and_print(f"📊 Total Discovered Types: {len(discovered_types)}")
            self.file_output.save_and_print(f"📋 Discovered Types: {', '.join(sorted(discovered_types))}")
            self.file_output.save_and_print(f"❓ Unknown Types: {', '.join(sorted(unknown_types)) if unknown_types else 'None'}")
            self.file_output.save_and_print(f"✅ Predefined Types Found: {len(discovered_types & predefined_types)}")
            self.file_output.save_and_print(f"❌ Missing Predefined Types: {', '.join(sorted(predefined_types - discovered_types))}")
            self.file_output.save_and_print(f"\n📈 Message Type Counts:")
            for msg_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                is_predefined = "✅" if msg_type in predefined_types else "❓"
                self.file_output.save_and_print(f"  {is_predefined} {msg_type}: {count}")
            self.file_output.save_and_print(f"\n🌐 Source Distribution:")
            for source, types in source_type_mapping.items():
                self.file_output.save_and_print(f"  {source}: {', '.join(sorted(types))}")
        
        summary_msg = (f"🔍 MessageType Analysis: {len(discovered_types)} total types, "
                      f"{len(unknown_types)} unknown types, {sum(type_counts.values())} total messages")
        logger.info(summary_msg)
        self.file_output.save_and_print(summary_msg)
        
        return analysis_result
    
    def _update_message_type_metrics(self, analysis_result: Dict):
        """Update Prometheus metrics with message type analysis"""
        if not analysis_result:
            return
            
        try:
            logger.info("🔍 Updating MessageType detection metrics...")
            self.file_output.save_and_print("🔍 Updating MessageType detection metrics...")
            
            # Update message type discovery info
            self.message_type_discovery.info({
                'total_discovered_types': str(analysis_result['total_discovered']),
                'discovered_types': ','.join(analysis_result['discovered_types']),
                'unknown_types': ','.join(analysis_result['unknown_types']),
                'missing_predefined_types': ','.join(analysis_result['missing_predefined']),
                'predefined_types_found': str(len(set(analysis_result['discovered_types']) & set(analysis_result['predefined_types']))),
                'last_scan_time': analysis_result['scan_time']
            })
            
            # Update message type counts
            for message_type, count in analysis_result['type_counts'].items():
                for source, source_types in analysis_result['source_type_mapping'].items():
                    if message_type in source_types:
                        is_predefined = str(message_type in self.OCPP_MESSAGE_TYPES).lower()
                        self.message_type_count.labels(
                            message_type=message_type,
                            data_source=source,
                            predefined=is_predefined
                        ).set(count)
            
            # Update discovery counters per source
            for source, types in analysis_result['source_type_mapping'].items():
                self.message_type_discovered.labels(data_source=source).inc(len(types))
            
            # Update unknown message type counters
            for unknown_type in analysis_result['unknown_types']:
                for source, source_types in analysis_result['source_type_mapping'].items():
                    if unknown_type in source_types:
                        self.unknown_message_types.labels(
                            message_type=unknown_type,
                            data_source=source
                        ).inc()
            
            success_msg = f"✅ MessageType metrics updated: {analysis_result['total_discovered']} types tracked"
            logger.info(success_msg)
            self.file_output.save_and_print(success_msg)
            
        except Exception as e:
            error_msg = f"❌ Failed to update MessageType metrics: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)
            if self.debug_mode:
                import traceback
                self.file_output.save_and_print("DEBUG: MessageType metrics error traceback:")
                self.file_output.save_and_print(traceback.format_exc())

    def _discover_all_message_types(self, time_range_hours=None) -> Dict:
        """Comprehensive message type discovery across all sources"""
        if time_range_hours is None:
            time_range_hours = self._calculate_time_range_hours()
            
        self.file_output.save_header(f"🔍 COMPREHENSIVE MESSAGETYPE DISCOVERY ({time_range_hours} hours)")
        logger.info(f"🔍 Starting comprehensive MessageType discovery scan ({time_range_hours} hours)...")
        
        # Calculate time range
        current_time_ms = int(time.time() * 1000)
        start_time_ms = current_time_ms - (time_range_hours * 60 * 60 * 1000)
        
        # Query to get ALL message types
        discovery_query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "mess.messType"}},
                        {
                            "range": {
                                "time": {
                                    "gte": start_time_ms
                                }
                            }
                        }
                    ]
                }
            },
            "aggs": {
                "message_types": {
                    "terms": {
                        "field": "mess.messType.keyword",
                        "size": 2000
                    }
                },
                "doc_message_types": {
                    "terms": {
                        "field": "messType.keyword", 
                        "size": 2000
                    }
                }
            },
            "sort": [{"time": {"order": "desc"}}],
            "size": 2000
        }
        
        logger.info("🔍 Executing comprehensive MessageType discovery query...")
        self.file_output.save_and_print("🔍 Executing comprehensive MessageType discovery query...")
        search_results = self._search_all_elasticsearch_sources(discovery_query)
        
        all_discovered_data = []
        aggregation_results = {}
        
        # Process results from each source
        for response, source_name in search_results:
            if not response or 'hits' not in response:
                continue
            
            hits = response['hits']['hits']
            discovery_msg = f"🔍 Source {source_name}: Found {len(hits)} records for MessageType discovery"
            logger.info(discovery_msg)
            self.file_output.save_and_print(discovery_msg)
            
            # Extract aggregation results
            aggs = response.get('aggregations', {})
            if aggs:
                aggregation_results[source_name] = {
                    'mess_types': [(bucket['key'], bucket['doc_count']) for bucket in aggs.get('message_types', {}).get('buckets', [])],
                    'doc_types': [(bucket['key'], bucket['doc_count']) for bucket in aggs.get('doc_message_types', {}).get('buckets', [])]
                }
            
            # Parse documents
            for hit in hits:
                source_doc = hit['_source']
                parsed = self._parse_document(source_doc, source_name)
                if parsed and parsed.get('message_type'):
                    all_discovered_data.append(parsed)
        
        # Analyze discovered data
        analysis_result = self._analyze_message_types(all_discovered_data)
        analysis_result['aggregations'] = aggregation_results
        
        # Print aggregation results
        if self.debug_mode:
            self.file_output.save_and_print(f"\n🔍 ELASTICSEARCH AGGREGATION RESULTS:")
            for source, agg_data in aggregation_results.items():
                self.file_output.save_and_print(f"\n📊 Source: {source}")
                self.file_output.save_and_print(f"  mess.messType field:")
                for msg_type, count in agg_data['mess_types']:
                    self.file_output.save_and_print(f"    {msg_type}: {count}")
                self.file_output.save_and_print(f"  messType field:")
                for msg_type, count in agg_data['doc_types']:
                    self.file_output.save_and_print(f"    {msg_type}: {count}")
        
        return analysis_result

    def _print_elasticsearch_data(self, hits: List[Dict], title: str = "Elasticsearch Data", source_name: str = "unknown"):
        """Print detailed Elasticsearch data for debugging"""
        if not self.debug_mode:
            return
            
        self.file_output.save_header(f"DEBUG: {title} (Source: {source_name})", "=", 90)
        
        for i, hit in enumerate(hits[:5]):  # Print first 5 records for inspection
            self.file_output.save_and_print(f"\n--- Record {i+1} from {source_name} ---")
            source = hit.get('_source', {})
            
            # Print document level info
            self.file_output.save_and_print(f"Document ID: {hit.get('_id', 'N/A')}")
            self.file_output.save_and_print(f"Index: {hit.get('_index', 'N/A')}")
            self.file_output.save_and_print(f"Score: {hit.get('_score', 'N/A')}")
            
            # Print document level fields
            self.file_output.save_and_print(f"pileCode: {source.get('pileCode', 'N/A')}")
            self.file_output.save_and_print(f"time: {source.get('time', 'N/A')} ({self._convert_ms_to_datetime(source.get('time', 0))})")
            self.file_output.save_and_print(f"type: {source.get('type', 'N/A')}")
            self.file_output.save_and_print(f"messType (doc level): {source.get('messType', 'N/A')}")
            
            # Print mess object details
            mess = source.get('mess', {})
            if mess:
                self.file_output.save_and_print(f"\nMess Object:")
                self.file_output.save_and_print(f"  identity: {mess.get('identity', 'N/A')}")
                self.file_output.save_and_print(f"  messType: {mess.get('messType', 'N/A')}")
                self.file_output.save_and_print(f"  connectorId: {mess.get('connectorId', 'N/A')}")
                self.file_output.save_and_print(f"  status: {mess.get('status', 'N/A')}")
                self.file_output.save_and_print(f"  errorCode: {mess.get('errorCode', 'N/A')}")
                self.file_output.save_and_print(f"  vendorId: {mess.get('vendorId', 'N/A')}")
                self.file_output.save_and_print(f"  vendorErrorCode: {mess.get('vendorErrorCode', 'N/A')}")
                self.file_output.save_and_print(f"  timestamp: {mess.get('timestamp', 'N/A')}")
                self.file_output.save_and_print(f"  info: {mess.get('info', 'N/A')}")
                self.file_output.save_and_print(f"  uniqueId: {mess.get('uniqueId', 'N/A')}")
                self.file_output.save_and_print(f"  transactionId: {mess.get('transactionId', 'N/A')}")
                self.file_output.save_and_print(f"  meterValue: {mess.get('meterValue', 'N/A')}")
                self.file_output.save_and_print(f"  idTag: {mess.get('idTag', 'N/A')}")
                self.file_output.save_and_print(f"  All mess fields: {list(mess.keys())}")
            else:
                self.file_output.save_and_print("  No mess object found!")
            
            # Print complete raw data for first record
            if i == 0:
                self.file_output.save_and_print(f"\nComplete Raw Data (Record 1 from {source_name}):")
                self.file_output.save_and_print(json.dumps(source, indent=2, ensure_ascii=False))
        
        if len(hits) > 5:
            self.file_output.save_and_print(f"\n... and {len(hits) - 5} more records from {source_name}")
        
    def _parse_document(self, doc: Dict, source_name: str = "unknown") -> Optional[Dict]:
        """Parse OCPP document with enhanced validation and debugging"""
        try:
            # Extract the mess object containing OCPP data
            mess = doc.get('mess', {})
            if not mess:
                if self.debug_mode:
                    debug_msg = f"DEBUG: No mess object found in document from {source_name}: {doc.get('_id', 'unknown')}"
                    self.file_output.save_and_print(debug_msg)
                return None
                
            # Validate message type
            message_type = mess.get('messType', '')
            if message_type not in self.OCPP_MESSAGE_TYPES and self.debug_mode:
                unknown_msg = f"DEBUG: Unknown OCPP message type from {source_name}: {message_type}"
                self.file_output.save_and_print(unknown_msg)
                
            # Parse the nested OCPP message
            parsed = {
                'identity': mess.get('identity', ''),
                'message_type': message_type,
                'connector_id': self._validate_connector_id(mess.get('connectorId', 0)),
                'status': mess.get('status', ''),
                'error_code': mess.get('errorCode', 'NoError'),
                'vendor_id': mess.get('vendorId', ''),
                'vendor_error_code': mess.get('vendorErrorCode', ''),
                'timestamp': mess.get('timestamp', ''),
                'info': mess.get('info', ''),
                'unique_id': mess.get('uniqueId', ''),
                # Transaction data
                'transaction_id': mess.get('transactionId'),
                'meter_value': mess.get('meterValue'),
                'id_tag': mess.get('idTag', ''),
                # Document level fields
                'pile_code': doc.get('pileCode', ''),
                'doc_time': doc.get('time', 0),  # Millisecond timestamp
                'doc_type': doc.get('type', 0),
                'doc_mess_type': doc.get('messType', ''),
                # Data source tracking
                'data_source': source_name
            }
            
            # Use pile_code as fallback for identity
            if not parsed['identity'] and parsed['pile_code']:
                parsed['identity'] = parsed['pile_code']
                if self.debug_mode:
                    fallback_msg = f"DEBUG: Using pileCode as identity from {source_name}: {parsed['pile_code']}"
                    self.file_output.save_and_print(fallback_msg)
                
            # Validate ChargePoint ID format
            if parsed['identity']:
                parsed['identity'] = self._normalize_chargepoint_id(parsed['identity'])
                
            # Debug print parsed data
            if self.debug_mode and parsed['identity']:
                parsed_msg = f"DEBUG: Parsed from {source_name} - ID: {parsed['identity']}, Type: {parsed['message_type']}, Status: {parsed['status']}, Connector: {parsed['connector_id']}"
                self.file_output.save_and_print(parsed_msg)
                
            return parsed
            
        except Exception as e:
            error_msg = f"Failed to parse OCPP document from {source_name}: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)
            if self.debug_mode:
                self.file_output.save_and_print(f"DEBUG: Parse error from {source_name} for document: {json.dumps(doc, indent=2)}")
            return None
    
    def _validate_connector_id(self, connector_id: Union[int, str]) -> int:
        """Validate and normalize connector ID according to OCPP 1.6"""
        try:
            conn_id = int(connector_id)
            # OCPP 1.6: connector_id 0 = whole ChargePoint, 1+ = individual connectors
            if conn_id < 0:
                if self.debug_mode:
                    invalid_msg = f"DEBUG: Invalid connector ID {conn_id}, setting to 0"
                    self.file_output.save_and_print(invalid_msg)
                return 0
            return conn_id
        except (ValueError, TypeError):
            if self.debug_mode:
                format_msg = f"DEBUG: Invalid connector ID format: {connector_id}"
                self.file_output.save_and_print(format_msg)
            return 0
    
    def _normalize_chargepoint_id(self, chargepoint_id: str) -> str:
        """Normalize ChargePoint ID according to OCPP 1.6 specification"""
        # Remove invalid characters and ensure length limits
        normalized = str(chargepoint_id).strip()
        if len(normalized) > 20:  # OCPP 1.6 limit
            normalized = normalized[:20]
            if self.debug_mode:
                truncate_msg = f"DEBUG: ChargePoint ID truncated to: {normalized}"
                self.file_output.save_and_print(truncate_msg)
        return normalized
    
    def _get_status_value(self, status: str) -> int:
        """Convert OCPP 1.6 status string to numeric value"""
        value = self.CHARGE_POINT_STATUS.get(status, -1)
        if value == -1 and self.debug_mode:
            unknown_status_msg = f"DEBUG: Unknown OCPP status: {status}"
            self.file_output.save_and_print(unknown_status_msg)
        return value
    
    def _get_error_code_value(self, error_code: str) -> int:
        """Convert OCPP 1.6 error code to numeric value"""
        value = self.OCPP_ERROR_CODES.get(error_code, -1)
        if value == -1 and self.debug_mode:
            unknown_error_msg = f"DEBUG: Unknown OCPP error code: {error_code}"
            self.file_output.save_and_print(unknown_error_msg)
        return value
    
    def _is_fault_status(self, status: str, error_code: str) -> bool:
        """Check if status indicates fault according to OCPP 1.6"""
        return status == 'Faulted' or error_code != 'NoError'
    
    def _convert_ms_to_datetime(self, ms_timestamp: int) -> str:
        """Convert millisecond timestamp to ISO 8601 datetime string"""
        try:
            return datetime.fromtimestamp(ms_timestamp / 1000).isoformat()
        except:
            return ""
    
    def _search_elasticsearch_source(self, source_config: Dict, query: Dict) -> Tuple[Dict, str]:
        """Search single Elasticsearch source with enhanced error handling and debugging"""
        source_name = source_config['name']
        try:
            url = f"{source_config['url']}/{self.target_index}/_search"
            headers = {'Content-Type': 'application/json'}
            
            if self.debug_mode:
                self.file_output.save_and_print(f"\nDEBUG: Elasticsearch Query for {source_name}:")
                self.file_output.save_and_print(json.dumps(query, indent=2))
                self.file_output.save_and_print(f"URL: {url}")
            
            response = requests.post(url, json=query, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if self.debug_mode:
                    total = result.get('hits', {}).get('total', 0)
                    if isinstance(total, dict):
                        total_count = total.get('value', 0)
                    else:
                        total_count = total
                    debug_response_msg = f"DEBUG: Source {source_name} returned {total_count} total hits"
                    self.file_output.save_and_print(debug_response_msg)
                return result, source_name
            else:
                error_msg = f"ES search failed for {source_name}: HTTP {response.status_code}"
                logger.error(error_msg)
                self.file_output.save_and_print(error_msg)
                if self.debug_mode:
                    self.file_output.save_and_print(f"DEBUG: ES Response from {source_name}: {response.text}")
                return {}, source_name
                
        except Exception as e:
            error_msg = f"ES search request failed for {source_name}: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)
            return {}, source_name
    
    def _search_all_elasticsearch_sources(self, query: Dict) -> List[Tuple[Dict, str]]:
        """Search all active Elasticsearch sources concurrently"""
        results = []
        
        # Update source status metrics
        for source_config in self.active_sources:
            self.source_status.labels(
                source_name=source_config['name'],
                source_host=source_config['host'],
                source_port=str(source_config['port'])
            ).set(1)
        
        # Use ThreadPoolExecutor for concurrent searches
        with ThreadPoolExecutor(max_workers=len(self.active_sources)) as executor:
            # Submit all search tasks
            future_to_source = {
                executor.submit(self._search_elasticsearch_source, source_config, query): source_config
                for source_config in self.active_sources
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_source):
                source_config = future_to_source[future]
                try:
                    result, source_name = future.result()
                    if result:
                        results.append((result, source_name))
                        success_msg = f"✅ Retrieved data from source: {source_name}"
                        logger.info(success_msg)
                        self.file_output.save_and_print(success_msg)
                    else:
                        warning_msg = f"⚠️ No data from source: {source_name}"
                        logger.warning(warning_msg)
                        self.file_output.save_and_print(warning_msg)
                        # Update source status to inactive
                        self.source_status.labels(
                            source_name=source_config['name'],
                            source_host=source_config['host'],
                            source_port=str(source_config['port'])
                        ).set(0)
                except Exception as e:
                    error_msg = f"Search failed for source {source_config['name']}: {e}"
                    logger.error(error_msg)
                    self.file_output.save_and_print(error_msg)
                    self.source_status.labels(
                        source_name=source_config['name'],
                        source_host=source_config['host'],
                        source_port=str(source_config['port'])
                    ).set(0)
        
        return results
    
    def _fetch_ocpp_data(self, time_range_hours=None) -> List[Dict]:
        """Fetch OCPP data from all sources with comprehensive debugging, deduplication, and transaction pairing analysis"""
        # Calculate time range in milliseconds
        if time_range_hours is None:
            time_range_hours = self._calculate_time_range_hours()
            
        current_time_ms = int(time.time() * 1000)
        start_time_ms = current_time_ms - (time_range_hours * 60 * 60 * 1000)
        
        self.file_output.save_header("OCPP DATA FETCH OPERATION")
        search_msg = f"Searching for OCPP data from {len(self.active_sources)} sources in last {time_range_hours} hours"
        logger.info(search_msg)
        self.file_output.save_and_print(search_msg)
        
        range_msg = f"Time range: {start_time_ms} to {current_time_ms} (ms)"
        logger.info(range_msg)
        self.file_output.save_and_print(range_msg)
        
        # Print query time window information
        if self.debug_mode:
            self._print_query_time_window(start_time_ms, current_time_ms, time_range_hours)
        
        # Primary query - Search for ALL message types
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"exists": {"field": "mess.messType"}},  # Search for ANY message type
                        {
                            "range": {
                                "time": {
                                    "gte": start_time_ms
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [{"time": {"order": "desc"}}],
            "size": 1000
        }
        
        query_msg = "Executing primary search for ALL OCPP message types across all sources..."
        logger.info(query_msg)
        self.file_output.save_and_print(query_msg)
        search_results = self._search_all_elasticsearch_sources(query)
        
        all_results = []
        source_record_counts = {}
        
        # Process results from each source
        for response, source_name in search_results:
            if not response or 'hits' not in response:
                continue
                
            hits = response['hits']['hits']
            total = response['hits']['total']
            
            if isinstance(total, dict):
                total_count = total.get('value', 0)
            else:
                total_count = total
            
            source_msg = f"Source {source_name}: Found {len(hits)} OCPP messages (total: {total_count})"
            logger.info(source_msg)
            self.file_output.save_and_print(source_msg)
            source_record_counts[source_name] = len(hits)
            
            # Update source records metric
            source_config = next((s for s in self.active_sources if s['name'] == source_name), None)
            if source_config:
                self.source_records_count.labels(
                    source_name=source_name,
                    source_host=source_config['host']
                ).set(len(hits))
            
            # PRINT ELASTICSEARCH DATA FROM EACH SOURCE
            if hits:
                self._print_elasticsearch_data(hits, f"Primary ALL Message Types Search Results", source_name)
                
                # Show latest data info for this source
                latest = hits[0]['_source']
                latest_time = latest.get('time', 0)
                latest_datetime = self._convert_ms_to_datetime(latest_time)
                latest_msg = f"Source {source_name} latest message time: {latest_datetime} ({latest_time})"
                logger.info(latest_msg)
                self.file_output.save_and_print(latest_msg)
                
                if 'mess' in latest:
                    mess = latest['mess']
                    mess_msg = f"Source {source_name} latest message: {mess.get('identity')} - {mess.get('status')} (connector {mess.get('connectorId')})"
                    logger.info(mess_msg)
                    self.file_output.save_and_print(mess_msg)
            
            # Parse all documents from this source
            parsed_count = 0
            for hit in hits:
                source_doc = hit['_source']
                parsed = self._parse_document(source_doc, source_name)
                
                if parsed and parsed.get('identity'):
                    parsed['es_index'] = hit['_index']
                    parsed['es_source'] = source_name
                    all_results.append(parsed)
                    parsed_count += 1
            
            if self.debug_mode:
                parse_msg = f"DEBUG: Source {source_name} successfully parsed {parsed_count} out of {len(hits)} documents"
                self.file_output.save_and_print(parse_msg)
        
        # Deduplicate results based on ChargePoint ID, connector, timestamp (keep most recent)
        deduped_results = self._deduplicate_records(all_results)
        
        # Perform transaction pairing analysis
        pairing_analysis = self._analyze_transaction_pairing(deduped_results, start_time_ms, current_time_ms)
        self._update_transaction_pairing_metrics(pairing_analysis)
        
        # Update discovery metrics and print summary
        unique_chargepoints = set(r.get('identity') for r in deduped_results if r.get('identity'))
        message_types = set(r.get('message_type') for r in deduped_results if r.get('message_type'))
        sources_with_data = set(r.get('data_source') for r in deduped_results if r.get('data_source'))
        
        # Print processing summary
        if self.debug_mode:
            self.file_output.save_header("MULTI-SOURCE PROCESSING SUMMARY")
            self.file_output.save_and_print(f"  Total records retrieved: {len(all_results)} (before deduplication)")
            self.file_output.save_and_print(f"  Total records after deduplication: {len(deduped_results)}")
            self.file_output.save_and_print(f"  Sources with data: {sorted(sources_with_data)}")
            self.file_output.save_and_print(f"  Source record counts: {source_record_counts}")
            self.file_output.save_and_print(f"  Unique ChargePoints: {len(unique_chargepoints)}")
            self.file_output.save_and_print(f"  ChargePoint IDs: {sorted(unique_chargepoints)}")
            self.file_output.save_and_print(f"  Message types found: {sorted(message_types)}")
            
            # Print sample of deduped results
            if deduped_results:
                self.file_output.save_and_print(f"\nDEBUG: Sample Deduplicated Results (first 3):")
                for i, result in enumerate(deduped_results[:3]):
                    sample_msg = (f"  {i+1}. ID: {result.get('identity')}, Source: {result.get('data_source')}, "
                                 f"Type: {result.get('message_type')}, Status: {result.get('status')}, "
                                 f"Time: {self._convert_ms_to_datetime(result.get('doc_time', 0))}")
                    self.file_output.save_and_print(sample_msg)
        
        # Update discovery metrics
        self.data_discovery.info({
            'total_records_raw': str(len(all_results)),
            'total_records_deduplicated': str(len(deduped_results)),
            'unique_chargepoints': str(len(unique_chargepoints)),
            'chargepoints_found': ','.join(sorted(unique_chargepoints)) if unique_chargepoints else 'none',
            'message_types_found': ','.join(sorted(message_types)) if message_types else 'none',
            'active_sources': ','.join(sorted(sources_with_data)) if sources_with_data else 'none',
            'source_record_counts': json.dumps(source_record_counts),
            'search_time_range_hours': str(time_range_hours),
            'last_search_time': datetime.now().isoformat()
        })
        
        completion_msg = "Multi-source OCPP data collection completed:"
        logger.info(completion_msg)
        self.file_output.save_and_print(completion_msg)
        
        summary_stats = [
            f"  Raw records: {len(all_results)}, Deduplicated: {len(deduped_results)}",
            f"  Unique ChargePoints: {sorted(unique_chargepoints)}",
            f"  Sources: {sorted(sources_with_data)}",
            f"  Message types: {sorted(message_types)}"
        ]
        
        for stat in summary_stats:
            logger.info(stat)
            self.file_output.save_and_print(stat)
        
        return deduped_results
    
    def _deduplicate_records(self, records: List[Dict]) -> List[Dict]:
        """Deduplicate records, keeping the most recent for each ChargePoint-Connector combination"""
        if not records:
            return []
        
        # Group by ChargePoint ID + Connector ID + Message Type
        grouped = {}
        for record in records:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}_{record.get('message_type', '')}"
            doc_time = record.get('doc_time', 0)
            
            if key not in grouped or doc_time > grouped[key].get('doc_time', 0):
                grouped[key] = record
        
        deduped = list(grouped.values())
        
        if self.debug_mode:
            dedup_msg = f"DEBUG: Deduplication: {len(records)} -> {len(deduped)} records"
            self.file_output.save_and_print(dedup_msg)
        
        return deduped
    
    def _update_metrics(self, data: List[Dict]):
        """Update Prometheus metrics with OCPP 1.6 compliant data from multiple sources"""
        try:
            if self.debug_mode:
                metrics_start_msg = f"\nDEBUG: Starting metrics update with {len(data)} records from multiple sources"
                self.file_output.save_and_print(metrics_start_msg)
            
            # Message type analysis (includes transaction pairing analysis done in _fetch_ocpp_data)
            message_type_analysis = self._analyze_message_types(data)
            self._update_message_type_metrics(message_type_analysis)
            
            # Group by chargepoint+connector for latest status
            chargepoint_latest_status = {}
            heartbeat_times = {}
            active_transactions = {}
            source_tracking = {}
            
            for record in data:
                chargepoint_id = record.get('identity', '')
                if not chargepoint_id:
                    continue
                    
                connector_id = str(record.get('connector_id', 0))
                doc_time = record.get('doc_time', 0)
                message_type = record.get('message_type', '')
                data_source = record.get('data_source', 'unknown')
                
                # Track which sources have data for each chargepoint
                if chargepoint_id not in source_tracking:
                    source_tracking[chargepoint_id] = set()
                source_tracking[chargepoint_id].add(data_source)
                
                # Update message counter
                direction = "from_chargepoint"
                self.ocpp_messages_total.labels(
                    chargepoint_id=chargepoint_id,
                    message_type=message_type,
                    direction=direction,
                    data_source=data_source
                ).inc()
                
                # Process StatusNotification messages
                if message_type == 'StatusNotification':
                    key = f"{chargepoint_id}_{connector_id}"
                    
                    if key not in chargepoint_latest_status or doc_time > chargepoint_latest_status[key].get('doc_time', 0):
                        chargepoint_latest_status[key] = record
                        if self.debug_mode:
                            status_update_msg = f"DEBUG: Updated latest status for {key} from {data_source}: {record.get('status')}"
                            self.file_output.save_and_print(status_update_msg)
                
                # Process Heartbeat messages
                elif message_type == 'Heartbeat':
                    hb_key = f"{chargepoint_id}_{data_source}"
                    if hb_key not in heartbeat_times or doc_time > heartbeat_times[hb_key]:
                        heartbeat_times[hb_key] = (doc_time, data_source, chargepoint_id)
                        if self.debug_mode:
                            hb_update_msg = f"DEBUG: Updated heartbeat for {chargepoint_id} from {data_source}: {self._convert_ms_to_datetime(doc_time)}"
                            self.file_output.save_and_print(hb_update_msg)
                
                # Process transaction messages
                elif message_type in ['StartTransaction', 'StopTransaction']:
                    tx_key = f"{chargepoint_id}_{data_source}"
                    if tx_key not in active_transactions:
                        active_transactions[tx_key] = 0
                    
                    if message_type == 'StartTransaction':
                        active_transactions[tx_key] += 1
                    elif message_type == 'StopTransaction':
                        active_transactions[tx_key] = max(0, active_transactions[tx_key] - 1)
                    
                    if self.debug_mode:
                        tx_update_msg = f"DEBUG: Transaction update for {chargepoint_id} from {data_source}: {active_transactions[tx_key]} active"
                        self.file_output.save_and_print(tx_update_msg)
            
            # Update status metrics
            updated_count = 0
            for key, record in chargepoint_latest_status.items():
                chargepoint_id = record.get('identity', '')
                connector_id = str(record.get('connector_id', 0))
                vendor_id = record.get('vendor_id', '')
                error_code = record.get('error_code', 'NoError')
                vendor_error_code = record.get('vendor_error_code', '')
                status = record.get('status', '')
                doc_time = record.get('doc_time', 0)
                data_source = record.get('data_source', 'unknown')
                
                status_time = self._convert_ms_to_datetime(doc_time)
                
                update_msg = f"Updating {chargepoint_id} connector {connector_id} from {data_source}: {status} at {status_time}"
                logger.info(update_msg)
                self.file_output.save_and_print(update_msg)
                
                # Update status value
                status_value = self._get_status_value(status)
                if status_value >= 0:
                    self.station_status.labels(
                        chargepoint_id=chargepoint_id,
                        connector_id=connector_id,
                        vendor_id=vendor_id,
                        data_source=data_source
                    ).set(status_value)
                
                # Update error code metric
                error_code_value = self._get_error_code_value(error_code)
                if error_code_value >= 0:
                    self.error_code_value.labels(
                        chargepoint_id=chargepoint_id,
                        connector_id=connector_id,
                        error_code=error_code,
                        data_source=data_source
                    ).set(error_code_value)
                
                # Update fault status
                is_fault = 1 if self._is_fault_status(status, error_code) else 0
                self.station_fault_status.labels(
                    chargepoint_id=chargepoint_id,
                    connector_id=connector_id,
                    error_code=error_code,
                    vendor_error_code=vendor_error_code,
                    data_source=data_source
                ).set(is_fault)
                
                # Log and count faults
                if is_fault:
                    fault_msg = f"OCPP FAULT from {data_source}: {chargepoint_id} connector {connector_id} - {error_code}"
                    logger.warning(fault_msg)
                    self.file_output.save_and_print(fault_msg)
                    self.fault_total.labels(
                        chargepoint_id=chargepoint_id,
                        connector_id=connector_id,
                        error_code=error_code,
                        data_source=data_source
                    ).inc()
                
                # Update last update time
                self.last_update.labels(
                    chargepoint_id=chargepoint_id, 
                    data_source=data_source
                ).set(doc_time / 1000)
                
                # Update ChargePoint info
                sources_for_cp = ','.join(sorted(source_tracking.get(chargepoint_id, set())))
                self.chargepoint_info.labels(
                    chargepoint_id=chargepoint_id,
                    data_source=data_source
                ).info({
                    'vendor_id': vendor_id,
                    'last_status': status,
                    'last_error_code': error_code,
                    'last_status_time': status_time,
                    'ocpp_version': '1.6',
                    'all_sources': sources_for_cp
                })
                
                updated_count += 1
            
            # Update heartbeat metrics
            for hb_key, (heartbeat_time, data_source, chargepoint_id) in heartbeat_times.items():
                self.last_heartbeat.labels(
                    chargepoint_id=chargepoint_id,
                    data_source=data_source
                ).set(heartbeat_time / 1000)
            
            # Update transaction metrics
            for tx_key, tx_count in active_transactions.items():
                chargepoint_id, data_source = tx_key.rsplit('_', 1)
                self.active_transactions.labels(
                    chargepoint_id=chargepoint_id,
                    data_source=data_source
                ).set(tx_count)
            
            self.last_update_time = datetime.now()
            
            metrics_completed_msg = f"Updated OCPP metrics for {updated_count} ChargePoint-Connector pairs across {len(self.active_sources)} sources"
            logger.info(metrics_completed_msg)
            self.file_output.save_and_print(metrics_completed_msg)
            
            if self.debug_mode:
                debug_summary = [
                    f"DEBUG: Multi-source metrics update completed:",
                    f"  Status updates: {updated_count}",
                    f"  Heartbeat updates: {len(heartbeat_times)}",
                    f"  Transaction updates: {len(active_transactions)}",
                    f"  Sources with data: {len(set(r.get('data_source', '') for r in data))}"
                ]
                for debug_line in debug_summary:
                    self.file_output.save_and_print(debug_line)
            
        except Exception as e:
            error_msg = f"Failed to update OCPP metrics: {e}"
            logger.error(error_msg)
            self.file_output.save_and_print(error_msg)
            if self.debug_mode:
                import traceback
                self.file_output.save_and_print("DEBUG: Multi-source metrics update error traceback:")
                self.file_output.save_and_print(traceback.format_exc())
    
    def collect_and_update(self):
        """Collect OCPP data from all sources and update metrics"""
        start_msg = "Starting OCPP 1.6 multi-source data collection..."
        logger.info(start_msg)
        self.file_output.save_and_print(start_msg)
        
        data = self._fetch_ocpp_data()
        
        if data:
            self._update_metrics(data)
            success_msg = "OCPP multi-source data collection and metrics update completed"
            logger.info(success_msg)
            self.file_output.save_and_print(success_msg)
        else:
            warning_msg = "No OCPP data retrieved from any source"
            logger.warning(warning_msg)
            self.file_output.save_and_print(warning_msg)
    
    def run_message_type_discovery(self):
        """Run comprehensive message type discovery"""
        discovery_start_msg = "🔍 Running comprehensive MessageType discovery..."
        logger.info(discovery_start_msg)
        self.file_output.save_and_print(discovery_start_msg)
        
        analysis_result = self._discover_all_message_types()
        self._update_message_type_metrics(analysis_result)
        
        discovery_completed_msg = "🔍 MessageType discovery completed"
        logger.info(discovery_completed_msg)
        self.file_output.save_and_print(discovery_completed_msg)
        
        return analysis_result

    def run_transaction_pairing_analysis(self, time_range_hours=None):
        """Run standalone transaction pairing analysis"""
        if time_range_hours is None:
            time_range_hours = self._calculate_time_range_hours()
            
        pairing_start_msg = f"🔄 Running standalone Transaction Pairing Analysis (range: {time_range_hours} hours)..."
        logger.info(pairing_start_msg)
        self.file_output.save_and_print(pairing_start_msg)
        
        data = self._fetch_ocpp_data(time_range_hours)
        if data:
            current_time_ms = int(time.time() * 1000)
            start_time_ms = current_time_ms - (time_range_hours * 60 * 60 * 1000)
            analysis_result = self._analyze_transaction_pairing(data, start_time_ms, current_time_ms)
            self._update_transaction_pairing_metrics(analysis_result)
            
            pairing_completed_msg = "🔄 Standalone Transaction Pairing Analysis completed"
            logger.info(pairing_completed_msg)
            self.file_output.save_and_print(pairing_completed_msg)
            
            return analysis_result
        else:
            no_data_msg = "No data available for Transaction Pairing Analysis"
            logger.warning(no_data_msg)
            self.file_output.save_and_print(no_data_msg)
            return {}

    def start_scheduler(self):
        """Start scheduled OCPP data collection from all sources"""
        self.file_output.save_header("STARTING SCHEDULER OPERATIONS")
        
        # Initial operations
        self.collect_and_update()
        self.run_message_type_discovery()
        self.run_transaction_pairing_analysis()
        
        # Schedule regular operations
        schedule.every(2).minutes.do(self.collect_and_update)  # Every 2 minutes to reduce load
        schedule.every().hour.do(self.run_message_type_discovery)
        schedule.every(30).minutes.do(self.run_transaction_pairing_analysis)
        
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(1)
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        scheduler_msgs = [
            f"OCPP multi-source data collection scheduler started (every 2 minutes) - {len(self.active_sources)} sources active",
            "🔍 MessageType discovery scheduler started (every hour)",
            "🔄 Transaction Pairing Analysis scheduler started (every 30 minutes)"
        ]
        
        for msg in scheduler_msgs:
            logger.info(msg)
            self.file_output.save_and_print(msg)
    
    def get_registry(self):
        """Get Prometheus metrics registry"""
        return self.registry

def main():
    """Main function for OCPP 1.6 multi-source exporter with Transaction Pairing Analysis and File Output"""
    try:
        # Configure multiple Elasticsearch sources
        es_sources = [
            {
                'name': 'primary',
                'host': '************',
                'port': 9200,
                'priority': 1
            },
            {
                'name': 'secondary', 
                'host': '*************',
                'port': 9200,
                'priority': 2
            }
        ]
        
        # Create output file with timestamp
        output_filename = f"ocpp_exporter_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # Create exporter with debug mode and file output
        exporter = OCCP16ChargingStationExporter(
            es_sources=es_sources, 
            debug_mode=True,
            output_file=output_filename
        )
        
        # Clear existing registry
        REGISTRY._collector_to_names.clear()
        REGISTRY._names_to_collectors.clear()
        
        # Start data collection scheduler
        exporter.start_scheduler()
        
        # Start Prometheus HTTP server
        start_http_server(8000, registry=exporter.get_registry())
        
        startup_msgs = [
            "OCPP 1.6 Multi-Source Prometheus exporter started, listening on port: 8000",
            "Metrics endpoint: http://localhost:8000/metrics",
            f"Active sources: {[s['name'] + '@' + s['host'] + ':' + str(s['port']) for s in exporter.active_sources]}",
            "🔍 MessageType Detection Module enabled",
            "🔄 Transaction Pairing Analysis Module enabled",
            f"📄 All output saved to: {output_filename}"
        ]
        
        for msg in startup_msgs:
            logger.info(msg)
            exporter.file_output.save_and_print(msg)
        
        # Keep running
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        shutdown_msg = "Received stop signal, shutting down OCPP multi-source exporter..."
        logger.info(shutdown_msg)
        print(shutdown_msg)
    except Exception as e:
        error_msg = f"OCPP multi-source exporter startup failed: {e}"
        logger.error(error_msg)
        print(error_msg)

if __name__ == "__main__":
    main()