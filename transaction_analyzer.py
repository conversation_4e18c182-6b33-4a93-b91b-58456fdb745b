#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Transaction Pairing Analysis Engine
事务配对分析引擎，负责分析StartTransaction和StopTransaction的配对情况
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from collections import defaultdict
from time_utils import convert_ms_to_datetime, is_time_near_boundary

logger = logging.getLogger(__name__)


class TransactionAnalyzer:
    """事务配对分析器"""
    
    def __init__(self, debug_mode: bool = False, file_output=None):
        """
        初始化事务配对分析器
        
        Args:
            debug_mode: 是否启用调试模式
            file_output: 文件输出管理器实例
        """
        self.debug_mode = debug_mode
        self.file_output = file_output
        self.unpaired_transactions = {}
        self.last_pairing_analysis = None
    
    def analyze_transaction_pairing(self, data: List[Dict], start_time_ms: int, end_time_ms: int) -> Dict:
        """
        分析StartTransaction和StopTransaction配对情况
        
        Args:
            data: 解析后的OCPP消息数据列表
            start_time_ms: 查询开始时间戳（毫秒）
            end_time_ms: 查询结束时间戳（毫秒）
            
        Returns:
            分析结果字典
        """
        if not data:
            return {}
        
        if self.file_output:
            self.file_output.save_header("🔄 TRANSACTION PAIRING ANALYSIS", "=", 100)
        logger.info("🔄 Starting Transaction Pairing Analysis...")
        
        # 分离事务消息
        start_transactions = []
        stop_transactions = []
        
        for record in data:
            message_type = record.get('message_type', '')
            if message_type == 'StartTransaction':
                start_transactions.append(record)
            elif message_type == 'StopTransaction':
                stop_transactions.append(record)
        
        info_msg = f"📊 Found {len(start_transactions)} StartTransaction and {len(stop_transactions)} StopTransaction messages"
        logger.info(info_msg)
        if self.file_output:
            self.file_output.save_and_print(info_msg)
        
        # 按ChargePoint + Connector分组
        start_by_cp_conn = defaultdict(list)
        stop_by_cp_conn = defaultdict(list)
        
        for record in start_transactions:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}"
            start_by_cp_conn[key].append(record)
            
        for record in stop_transactions:
            key = f"{record.get('identity', '')}_{record.get('connector_id', 0)}"
            stop_by_cp_conn[key].append(record)
        
        # 查找未配对的事务
        unpaired_starts = {}
        unpaired_stops = {}
        all_chargepoints = set(list(start_by_cp_conn.keys()) + list(stop_by_cp_conn.keys()))
        
        for cp_conn_key in all_chargepoints:
            starts = start_by_cp_conn.get(cp_conn_key, [])
            stops = stop_by_cp_conn.get(cp_conn_key, [])
            
            start_count = len(starts)
            stop_count = len(stops)
            
            if start_count != stop_count:
                chargepoint_id, connector_id = cp_conn_key.rsplit('_', 1)
                
                if start_count > stop_count:
                    unpaired_starts[cp_conn_key] = {
                        'chargepoint_id': chargepoint_id,
                        'connector_id': connector_id,
                        'start_count': start_count,
                        'stop_count': stop_count,
                        'unpaired_count': start_count - stop_count,
                        'start_records': starts,
                        'stop_records': stops,
                        'data_sources': list(set([r.get('data_source', 'unknown') for r in starts + stops]))
                    }
                    
                if stop_count > start_count:
                    unpaired_stops[cp_conn_key] = {
                        'chargepoint_id': chargepoint_id,
                        'connector_id': connector_id,
                        'start_count': start_count,
                        'stop_count': stop_count,
                        'unpaired_count': stop_count - start_count,
                        'start_records': starts,
                        'stop_records': stops,
                        'data_sources': list(set([r.get('data_source', 'unknown') for r in starts + stops]))
                    }
        
        # 创建分析结果
        analysis_result = {
            'total_start_transactions': len(start_transactions),
            'total_stop_transactions': len(stop_transactions),
            'unpaired_starts': unpaired_starts,
            'unpaired_stops': unpaired_stops,
            'chargepoints_with_unpaired': len(set(list(unpaired_starts.keys()) + list(unpaired_stops.keys()))),
            'query_start_time_ms': start_time_ms,
            'query_end_time_ms': end_time_ms,
            'query_start_datetime': convert_ms_to_datetime(start_time_ms),
            'query_end_datetime': convert_ms_to_datetime(end_time_ms),
            'analysis_time': datetime.now().isoformat()
        }
        
        # 打印详细分析
        if self.debug_mode and self.file_output:
            self._print_detailed_analysis(analysis_result, start_time_ms, end_time_ms)
        
        # 更新类级别跟踪
        self.unpaired_transactions = {
            'unpaired_starts': unpaired_starts,
            'unpaired_stops': unpaired_stops
        }
        self.last_pairing_analysis = datetime.now()
        
        summary_msg = (f"🔄 Transaction Pairing Analysis: {len(unpaired_starts)} unpaired starts, "
                      f"{len(unpaired_stops)} unpaired stops, "
                      f"{analysis_result['chargepoints_with_unpaired']} affected ChargePoints")
        logger.info(summary_msg)
        if self.file_output:
            self.file_output.save_and_print(summary_msg)
        
        return analysis_result
    
    def _print_detailed_analysis(self, analysis_result: Dict, start_time_ms: int, end_time_ms: int):
        """打印详细的分析结果"""
        if not self.file_output:
            return
            
        self.file_output.save_and_print(f"📅 Query Time Window:")
        self.file_output.save_and_print(f"   Start: {analysis_result['query_start_datetime']} ({start_time_ms})")
        self.file_output.save_and_print(f"   End:   {analysis_result['query_end_datetime']} ({end_time_ms})")
        self.file_output.save_and_print(f"📊 Transaction Counts:")
        self.file_output.save_and_print(f"   StartTransaction: {analysis_result['total_start_transactions']}")
        self.file_output.save_and_print(f"   StopTransaction: {analysis_result['total_stop_transactions']}")
        self.file_output.save_and_print(f"   Difference: {analysis_result['total_stop_transactions'] - analysis_result['total_start_transactions']}")
        
        unpaired_starts = analysis_result['unpaired_starts']
        unpaired_stops = analysis_result['unpaired_stops']
        
        if unpaired_starts:
            self.file_output.save_and_print(f"\n⚠️  UNPAIRED START TRANSACTIONS ({len(unpaired_starts)} ChargePoint-Connector pairs):")
            for cp_conn, info in unpaired_starts.items():
                self.file_output.save_and_print(f"   🔌 {info['chargepoint_id']} Connector {info['connector_id']}:")
                self.file_output.save_and_print(f"      Start: {info['start_count']}, Stop: {info['stop_count']}, Unpaired: {info['unpaired_count']}")
                self.file_output.save_and_print(f"      Sources: {', '.join(info['data_sources'])}")
                
                # 显示事务时间以检查是否接近时间窗口边界
                self.file_output.save_and_print(f"      StartTransaction times:")
                for start_rec in info['start_records'][-3:]:  # 显示最后3个
                    tx_time = start_rec.get('doc_time', 0)
                    tx_datetime = convert_ms_to_datetime(tx_time)
                    near_start = is_time_near_boundary(tx_time, start_time_ms, 1)
                    near_end = is_time_near_boundary(tx_time, end_time_ms, 1)
                    boundary_info = "🚨 NEAR WINDOW BOUNDARY" if (near_start or near_end) else ""
                    self.file_output.save_and_print(f"        📅 {tx_datetime} ({tx_time}) {boundary_info}")
        
        if unpaired_stops:
            self.file_output.save_and_print(f"\n⚠️  UNPAIRED STOP TRANSACTIONS ({len(unpaired_stops)} ChargePoint-Connector pairs):")
            for cp_conn, info in unpaired_stops.items():
                self.file_output.save_and_print(f"   🔌 {info['chargepoint_id']} Connector {info['connector_id']}:")
                self.file_output.save_and_print(f"      Start: {info['start_count']}, Stop: {info['stop_count']}, Unpaired: {info['unpaired_count']}")
                self.file_output.save_and_print(f"      Sources: {', '.join(info['data_sources'])}")
                
                # 显示事务时间以检查是否接近时间窗口边界
                self.file_output.save_and_print(f"      StopTransaction times:")
                for stop_rec in info['stop_records'][-3:]:  # 显示最后3个
                    tx_time = stop_rec.get('doc_time', 0)
                    tx_datetime = convert_ms_to_datetime(tx_time)
                    near_start = is_time_near_boundary(tx_time, start_time_ms, 1)
                    near_end = is_time_near_boundary(tx_time, end_time_ms, 1)
                    boundary_info = "🚨 NEAR WINDOW BOUNDARY" if (near_start or near_end) else ""
                    self.file_output.save_and_print(f"        📅 {tx_datetime} ({tx_time}) {boundary_info}")
        
        if not unpaired_starts and not unpaired_stops:
            self.file_output.save_and_print(f"\n✅ ALL TRANSACTIONS ARE PROPERLY PAIRED!")
    
    def get_unpaired_transactions(self) -> Dict:
        """获取未配对的事务信息"""
        return self.unpaired_transactions
    
    def get_last_analysis_time(self) -> Optional[datetime]:
        """获取最后一次分析时间"""
        return self.last_pairing_analysis
    
    def analyze_transaction_by_chargepoint(self, data: List[Dict], chargepoint_id: str) -> Dict:
        """
        分析特定ChargePoint的事务配对情况
        
        Args:
            data: 解析后的OCPP消息数据列表
            chargepoint_id: ChargePoint ID
            
        Returns:
            特定ChargePoint的分析结果
        """
        # 过滤特定ChargePoint的数据
        cp_data = [record for record in data if record.get('identity') == chargepoint_id]
        
        if not cp_data:
            return {'error': f'No data found for ChargePoint: {chargepoint_id}'}
        
        # 按连接器分组分析
        connectors = set(record.get('connector_id', 0) for record in cp_data)
        connector_analysis = {}
        
        for connector_id in connectors:
            conn_data = [record for record in cp_data if record.get('connector_id') == connector_id]
            
            start_txs = [r for r in conn_data if r.get('message_type') == 'StartTransaction']
            stop_txs = [r for r in conn_data if r.get('message_type') == 'StopTransaction']
            
            connector_analysis[connector_id] = {
                'start_count': len(start_txs),
                'stop_count': len(stop_txs),
                'is_balanced': len(start_txs) == len(stop_txs),
                'difference': len(stop_txs) - len(start_txs),
                'start_transactions': start_txs,
                'stop_transactions': stop_txs
            }
        
        return {
            'chargepoint_id': chargepoint_id,
            'total_connectors': len(connectors),
            'connector_analysis': connector_analysis,
            'analysis_time': datetime.now().isoformat()
        }
    
    def get_transaction_statistics(self, data: List[Dict]) -> Dict:
        """
        获取事务统计信息
        
        Args:
            data: 解析后的OCPP消息数据列表
            
        Returns:
            事务统计信息字典
        """
        start_txs = [r for r in data if r.get('message_type') == 'StartTransaction']
        stop_txs = [r for r in data if r.get('message_type') == 'StopTransaction']
        
        # 按数据源统计
        source_stats = defaultdict(lambda: {'start': 0, 'stop': 0})
        for tx in start_txs:
            source_stats[tx.get('data_source', 'unknown')]['start'] += 1
        for tx in stop_txs:
            source_stats[tx.get('data_source', 'unknown')]['stop'] += 1
        
        # 按ChargePoint统计
        cp_stats = defaultdict(lambda: {'start': 0, 'stop': 0})
        for tx in start_txs:
            cp_stats[tx.get('identity', 'unknown')]['start'] += 1
        for tx in stop_txs:
            cp_stats[tx.get('identity', 'unknown')]['stop'] += 1
        
        return {
            'total_start_transactions': len(start_txs),
            'total_stop_transactions': len(stop_txs),
            'total_difference': len(stop_txs) - len(start_txs),
            'source_statistics': dict(source_stats),
            'chargepoint_statistics': dict(cp_stats),
            'unique_chargepoints': len(cp_stats),
            'unique_sources': len(source_stats)
        }


def create_analyzer(debug_mode: bool = False, file_output=None) -> TransactionAnalyzer:
    """
    创建事务分析器实例
    
    Args:
        debug_mode: 是否启用调试模式
        file_output: 文件输出管理器实例
        
    Returns:
        事务分析器实例
    """
    return TransactionAnalyzer(debug_mode=debug_mode, file_output=file_output)
